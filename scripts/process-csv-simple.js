/**
 * Script sederhana untuk mengkonversi CSV data awal ke format seed data Supabase
 * Dashboard Sistem Magang Jepang
 */

const fs = require('fs');
const path = require('path');

// Fungsi untuk membersihkan data
function cleanData(value) {
    if (!value || value === '' || value === '#REF!' || value === '#N/A' || value === 'undefined') {
        return null;
    }
    return value.toString().trim();
}

// Fungsi untuk generate UUID sederhana
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Fungsi untuk mengkonversi tanggal Excel ke format ISO
function excelDateToISO(excelDate) {
    if (!excelDate || isNaN(excelDate)) return null;
    
    // Excel date serial number (1 = 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(excelEpoch.getTime() + (excelDate - 2) * 24 * 60 * 60 * 1000);
    return date.toISOString().split('T')[0];
}

// Fungsi untuk menghitung umur dari tanggal lahir
function calculateAge(birthDate) {
    if (!birthDate) return 25; // default age
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }
    return age;
}

// Main processing function
async function processCSV() {
    try {
        console.log('🔄 Reading CSV file...');
        const csvPath = path.join(__dirname, '..', 'seed data awal 2025.csv');
        const csvContent = fs.readFileSync(csvPath, 'utf-8');
        
        const lines = csvContent.split('\n');
        console.log(`📊 Found ${lines.length} lines in CSV`);
        
        // Data containers
        const kumiai = new Map();
        const perusahaan = new Map();
        const lpk = new Map();
        const siswa = [];
        const penempatan = [];
        
        let processedRows = 0;
        let skippedRows = 0;
        
        // Process each line (skip header)
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;
            
            const columns = line.split(';');
            if (columns.length < 10) {
                skippedRows++;
                continue;
            }
            
            // Extract data from columns
            const nama = cleanData(columns[1]);
            const nik = cleanData(columns[2]);
            const namaKumiai = cleanData(columns[4]);
            const kodeKumiai = cleanData(columns[5]);
            const perusahaanPenerima = cleanData(columns[7]);
            const gender = cleanData(columns[8]);
            const lpkName = cleanData(columns[9]);
            const tempatLahir = cleanData(columns[10]);
            const tglLahir = cleanData(columns[11]);
            const email = cleanData(columns[12]);
            const alamat = cleanData(columns[13]);
            const noHp = cleanData(columns[15]);
            const alamatPerusahaan = cleanData(columns[17]);
            const prefektur = cleanData(columns[18]);
            const jenisPekerjaan = cleanData(columns[19]);
            const keterangan = cleanData(columns[25]);
            const kotaDaerah = cleanData(columns[42]);
            
            // Skip if no name or NIK
            if (!nama || !nik) {
                skippedRows++;
                continue;
            }
            
            processedRows++;
            
            // Process Kumiai
            if (namaKumiai && kodeKumiai && !kumiai.has(kodeKumiai)) {
                kumiai.set(kodeKumiai, {
                    id: generateUUID(),
                    nama_kumiai: namaKumiai,
                    kode_kumiai: kodeKumiai,
                    alamat_jepang: 'Alamat akan diupdate sesuai data lengkap',
                    kota_jepang: 'Tokyo',
                    prefektur: prefektur || 'Tokyo',
                    kontak_person: 'Kontak Person Kumiai',
                    nomor_telepon: '+81-3-0000-0000',
                    email: null,
                    website: null,
                    status: 'aktif',
                    keterangan: `Kumiai untuk ${namaKumiai}`
                });
            }
            
            // Process LPK
            if (lpkName && !lpk.has(lpkName)) {
                lpk.set(lpkName, {
                    id: generateUUID(),
                    nama_lpk: lpkName,
                    alamat_lengkap: 'Alamat akan diupdate sesuai data lengkap',
                    kota: kotaDaerah || 'Jakarta',
                    provinsi: 'Jawa Tengah',
                    nama_pimpinan: 'Pimpinan LPK',
                    kontak_person: 'Kontak Person LPK',
                    nomor_telepon: '021-0000-0000',
                    email: null,
                    website: null,
                    status: 'aktif',
                    tanggal_kerjasama: '2024-01-01',
                    catatan: `LPK untuk daerah ${kotaDaerah || 'Jakarta'}`
                });
            }
            
            // Process Perusahaan
            if (perusahaanPenerima && kodeKumiai && !perusahaan.has(perusahaanPenerima)) {
                const kumiaiData = kumiai.get(kodeKumiai);
                if (kumiaiData) {
                    perusahaan.set(perusahaanPenerima, {
                        id: generateUUID(),
                        kumiai_id: kumiaiData.id,
                        nama_perusahaan: perusahaanPenerima,
                        alamat_jepang: alamatPerusahaan || 'Alamat akan diupdate',
                        kota_jepang: 'Tokyo',
                        prefektur: prefektur || 'Tokyo',
                        bidang_usaha: jenisPekerjaan || 'Manufacturing',
                        kontak_person: 'Kontak Person Perusahaan',
                        nomor_telepon: '+81-3-0000-0000',
                        email: null,
                        website: null,
                        status: 'aktif',
                        keterangan: `Perusahaan di bidang ${jenisPekerjaan || 'Manufacturing'}`
                    });
                }
            }
            
            // Process Siswa
            const lpkData = lpk.get(lpkName);
            if (lpkData) {
                const birthDate = excelDateToISO(tglLahir) || '1995-01-01';
                const siswaData = {
                    id: generateUUID(),
                    lpk_id: lpkData.id,
                    nama_lengkap: nama,
                    nik: nik,
                    tempat_lahir: tempatLahir || 'Jakarta',
                    tanggal_lahir: birthDate,
                    jenis_kelamin: gender === 'Laki - Laki' ? 'L' : 'P',
                    agama: 'Islam',
                    status_pernikahan: 'belum_menikah',
                    alamat_lengkap: alamat || 'Alamat akan diupdate',
                    kelurahan: 'Kelurahan',
                    kecamatan: 'Kecamatan',
                    kota_kabupaten: kotaDaerah || 'Jakarta',
                    provinsi: 'Jawa Tengah',
                    kode_pos: '12345',
                    nomor_hp: noHp || '081234567890',
                    email: email,
                    pendidikan_terakhir: 'SMA',
                    nama_sekolah: 'SMA Negeri 1',
                    tahun_lulus: 2018,
                    jurusan: 'IPA',
                    nama_ayah: 'Nama Ayah',
                    nama_ibu: 'Nama Ibu',
                    alamat_keluarga: alamat || 'Alamat keluarga',
                    nomor_hp_keluarga: noHp || '081234567890',
                    status_pendaftaran: 'approved',
                    tanggal_daftar: '2024-01-01',
                    catatan: `Data dari CSV - ${keterangan || 'Tidak ada keterangan'}`
                };
                
                siswa.push(siswaData);
                
                // Process Penempatan jika siswa sudah bekerja
                if (keterangan === 'Kerja' || keterangan === 'Bekerja') {
                    const perusahaanData = perusahaan.get(perusahaanPenerima);
                    const kumiaiData = kumiai.get(kodeKumiai);
                    
                    if (perusahaanData && kumiaiData) {
                        penempatan.push({
                            id: generateUUID(),
                            siswa_id: siswaData.id,
                            perusahaan_id: perusahaanData.id,
                            kumiai_id: kumiaiData.id,
                            tanggal_penempatan: '2024-01-01',
                            tanggal_keberangkatan: '2024-06-01',
                            tanggal_kepulangan: null,
                            status_penempatan: 'aktif',
                            posisi_kerja: jenisPekerjaan || 'General Worker',
                            gaji_aktual: 180000,
                            alamat_kerja: alamatPerusahaan || 'Alamat kerja di Jepang',
                            evaluasi_bulanan: null,
                            catatan_khusus: `Penempatan melalui ${namaKumiai}`
                        });
                    }
                }
            }
        }
        
        console.log(`✅ Processing completed:`);
        console.log(`   - Processed: ${processedRows} rows`);
        console.log(`   - Skipped: ${skippedRows} rows`);
        console.log(`   - Kumiai: ${kumiai.size}`);
        console.log(`   - Perusahaan: ${perusahaan.size}`);
        console.log(`   - LPK: ${lpk.size}`);
        console.log(`   - Siswa: ${siswa.length}`);
        console.log(`   - Penempatan: ${penempatan.length}`);
        
        // Generate SQL
        console.log('🔄 Generating SQL...');
        const sql = generateSQL({
            kumiai: Array.from(kumiai.values()),
            perusahaan: Array.from(perusahaan.values()),
            lpk: Array.from(lpk.values()),
            siswa,
            penempatan
        });
        
        // Save files
        const sqlPath = path.join(__dirname, 'seed-data-complete.sql');
        const jsonPath = path.join(__dirname, 'seed-data-complete.json');
        
        fs.writeFileSync(sqlPath, sql);
        fs.writeFileSync(jsonPath, JSON.stringify({
            kumiai: Array.from(kumiai.values()),
            perusahaan: Array.from(perusahaan.values()),
            lpk: Array.from(lpk.values()),
            siswa,
            penempatan
        }, null, 2));
        
        console.log(`✅ Files generated:`);
        console.log(`   - SQL: ${sqlPath}`);
        console.log(`   - JSON: ${jsonPath}`);
        
    } catch (error) {
        console.error('❌ Error processing CSV:', error);
    }
}

// Generate SQL function
function generateSQL(data) {
    let sql = `-- Seed data generated from CSV
-- Dashboard Sistem Magang Jepang
-- Generated on: ${new Date().toISOString()}
-- Total records: Kumiai(${data.kumiai.length}), Perusahaan(${data.perusahaan.length}), LPK(${data.lpk.length}), Siswa(${data.siswa.length}), Penempatan(${data.penempatan.length})

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- Clear existing data (optional - uncomment if needed)
-- TRUNCATE TABLE penempatan_siswa CASCADE;
-- TRUNCATE TABLE siswa CASCADE;
-- TRUNCATE TABLE perusahaan_penerima CASCADE;
-- TRUNCATE TABLE lpk_mitra CASCADE;
-- TRUNCATE TABLE kumiai CASCADE;

`;

    // Insert Kumiai
    sql += `-- Insert Kumiai data (${data.kumiai.length} records)\n`;
    data.kumiai.forEach(item => {
        sql += `INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES 
('${item.id}', '${item.nama_kumiai.replace(/'/g, "''")}', '${item.kode_kumiai}', '${item.alamat_jepang.replace(/'/g, "''")}', '${item.kota_jepang}', '${item.prefektur}', '${item.kontak_person}', '${item.nomor_telepon}', ${item.email ? `'${item.email}'` : 'NULL'}, ${item.website ? `'${item.website}'` : 'NULL'}, '${item.status}', '${item.keterangan.replace(/'/g, "''")}');\n`;
    });
    
    // Insert LPK
    sql += `\n-- Insert LPK data (${data.lpk.length} records)\n`;
    data.lpk.forEach(item => {
        sql += `INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES 
('${item.id}', '${item.nama_lpk.replace(/'/g, "''")}', '${item.alamat_lengkap.replace(/'/g, "''")}', '${item.kota}', '${item.provinsi}', '${item.nama_pimpinan}', '${item.kontak_person}', '${item.nomor_telepon}', ${item.email ? `'${item.email}'` : 'NULL'}, ${item.website ? `'${item.website}'` : 'NULL'}, '${item.status}', '${item.tanggal_kerjasama}', '${item.catatan.replace(/'/g, "''")}');\n`;
    });
    
    // Insert Perusahaan
    sql += `\n-- Insert Perusahaan data (${data.perusahaan.length} records)\n`;
    data.perusahaan.forEach(item => {
        sql += `INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES 
('${item.id}', '${item.kumiai_id}', '${item.nama_perusahaan.replace(/'/g, "''")}', '${item.alamat_jepang.replace(/'/g, "''")}', '${item.kota_jepang}', '${item.prefektur}', '${item.bidang_usaha.replace(/'/g, "''")}', '${item.kontak_person}', '${item.nomor_telepon}', ${item.email ? `'${item.email}'` : 'NULL'}, ${item.website ? `'${item.website}'` : 'NULL'}, '${item.status}', '${item.keterangan.replace(/'/g, "''")}');\n`;
    });
    
    // Insert Siswa
    sql += `\n-- Insert Siswa data (${data.siswa.length} records)\n`;
    data.siswa.forEach(item => {
        sql += `INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES 
('${item.id}', '${item.lpk_id}', '${item.nama_lengkap.replace(/'/g, "''")}', '${item.nik}', '${item.tempat_lahir}', '${item.tanggal_lahir}', '${item.jenis_kelamin}', '${item.agama}', '${item.status_pernikahan}', '${item.alamat_lengkap.replace(/'/g, "''")}', '${item.kelurahan}', '${item.kecamatan}', '${item.kota_kabupaten}', '${item.provinsi}', '${item.kode_pos}', '${item.nomor_hp}', ${item.email ? `'${item.email}'` : 'NULL'}, '${item.pendidikan_terakhir}', '${item.nama_sekolah}', ${item.tahun_lulus}, '${item.jurusan}', '${item.nama_ayah}', '${item.nama_ibu}', '${item.alamat_keluarga.replace(/'/g, "''")}', '${item.nomor_hp_keluarga}', '${item.status_pendaftaran}', '${item.tanggal_daftar}', '${item.catatan.replace(/'/g, "''")}');\n`;
    });
    
    // Insert Penempatan
    sql += `\n-- Insert Penempatan data (${data.penempatan.length} records)\n`;
    data.penempatan.forEach(item => {
        sql += `INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES 
('${item.id}', '${item.siswa_id}', '${item.perusahaan_id}', '${item.kumiai_id}', '${item.tanggal_penempatan}', '${item.tanggal_keberangkatan}', ${item.tanggal_kepulangan ? `'${item.tanggal_kepulangan}'` : 'NULL'}, '${item.status_penempatan}', '${item.posisi_kerja.replace(/'/g, "''")}', ${item.gaji_aktual}, '${item.alamat_kerja.replace(/'/g, "''")}', ${item.evaluasi_bulanan ? `'${item.evaluasi_bulanan}'` : 'NULL'}, '${item.catatan_khusus.replace(/'/g, "''")}');\n`;
    });
    
    sql += `\n-- Re-enable foreign key checks\nSET session_replication_role = DEFAULT;\n\n-- Seed data import completed successfully!\n`;
    
    return sql;
}

// Run the script
if (require.main === module) {
    processCSV();
}

module.exports = { processCSV };

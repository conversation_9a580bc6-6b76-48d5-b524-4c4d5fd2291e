-- Seed Data Dashboard Magang <PERSON> - Yutaka LPK
-- Generated: 2025-07-11T04:42:07.151Z
-- Records: <PERSON><PERSON>(2), <PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON><PERSON>(16)

-- Disable foreign key checks
SET session_replication_role = replica;

-- Insert LPK Mitra
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('336c678c-096d-4297-aeb0-ecd4c86f140b', 'Yutaka', 'Jl. Raya Utama No. 123, Jakarta Pusat 10110', 'Jakarta', 'DKI Jakarta', 'Bapak <PERSON>', 'Ibu Sa<PERSON> Wijaya', '021-1234-5678', '<EMAIL>', 'https://yutaka.co.id', 'aktif', '2024-01-01', '<PERSON><PERSON> Yu<PERSON>ka - Partner utama untuk program magang <PERSON>');
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('08d60fbc-eca2-4151-9edc-80f926c50866', 'LPK Dummy', 'Jl. Pendidikan No. 456, Bandung 40123', 'Bandung', 'Jawa Barat', 'Bapak Pimpinan LPK', 'Ibu Kontak Person', '022-9876-5432', '<EMAIL>', 'https://lpkdummy.co.id', 'aktif', '2024-01-01', 'LPK Dummy untuk testing dan development');

-- Insert Kumiai
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', 'Gokei Cloud Kyodo Kumiai', 'GOKEI', '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002', 'Tokyo', 'Tokyo', 'Tanaka San', '+81-3-1234-5678', '<EMAIL>', 'https://gokei.jp', 'aktif', 'Kumiai utama untuk program magang');
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('e160f601-b0f0-43f2-abbb-40f210c5502d', 'TIC Kyodo Kumiai', 'TIC', '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022', 'Tokyo', 'Tokyo', 'Yamada San', '+81-3-2345-6789', '<EMAIL>', 'https://tic.jp', 'aktif', 'Kumiai untuk bidang teknik');

-- Insert Perusahaan
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('d931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', 'Tokyo Manufacturing Co., Ltd.', '3-3-3 Minato, Minato-ku, Tokyo 105-0003', 'Tokyo', 'Tokyo', 'Manufacturing', 'Sato San', '+81-3-3456-7890', '<EMAIL>', 'https://tokyo-mfg.jp', 'aktif', 'Perusahaan manufaktur terkemuka di Tokyo');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', 'Osaka Technical Industries', '4-4-4 Namba, Chuo-ku, Osaka 542-0076', 'Osaka', 'Osaka', 'Technical Services', 'Suzuki San', '+81-6-4567-8901', '<EMAIL>', 'https://osaka-tech.jp', 'aktif', 'Perusahaan teknik di Osaka');

-- Insert Siswa
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8654be3e-f238-434e-8764-020c4d7641f1', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'GULAM FATHUR RISQI', '3301092611020001', 'CILACAP', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GULAM', 'Ibu GULAM', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 1');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('dfd42a9f-8c30-4b6a-a05c-c65c9da3607d', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'ADITYA HERI PURNOMO', '3301240603030001', 'CILACAP', '2003-03-06', 'L', 'Islam', 'belum_menikah', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITYA', 'Ibu ADITYA', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 2');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2eb8c87c-7c77-4d91-ac52-3e6912ae5ae9', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'BRIYAN BINTORO', '3403010607990003', 'GUNUNG KIDUL', '1999-07-06', 'L', 'Islam', 'belum_menikah', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Gunung Kidul', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BRIYAN', 'Ibu BRIYAN', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 3');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('23ea8c8b-a601-4311-85fe-c64c5f25b2ab', '08d60fbc-eca2-4151-9edc-80f926c50866', 'RIO DWI SATRIOTOMO', '3322031212030002', 'KAB. SEMARANG', '1905-06-25', 'L', 'Islam', 'belum_menikah', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIO', 'Ibu RIO', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 4');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1ce5654b-9f9c-49d5-8233-16637525fd50', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'DIMAS WAHYU LUWANGGA PRASETYA', '3322081912000001', 'KAB. SEMARANG', '2000-12-19', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DIMAS', 'Ibu DIMAS', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 5');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('eb05889f-a905-4cfd-afe7-074387d7f2ca', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'MIRZA ALI HILMI', '3328111510020004', 'Tegal', '2002-10-15', 'L', 'Islam', 'belum_menikah', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Tegal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MIRZA', 'Ibu MIRZA', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 6');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('bc4a49d1-6ee6-4b92-8339-39d6f495e9ce', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'LANGGA PRATAMA', '3301090701050001', 'Cilacap', '2005-01-07', 'L', 'Islam', 'belum_menikah', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LANGGA', 'Ibu LANGGA', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 7');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d575ef84-5da4-4963-b4d5-1b41e33f109d', '08d60fbc-eca2-4151-9edc-80f926c50866', 'ADI NUR MUHAMMAD SAPUTRA', '3324110111010003', 'KENDAL', '2001-11-01', 'L', 'Islam', 'belum_menikah', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADI', 'Ibu ADI', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 8');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4cdba98e-8ba8-4354-b809-1a8a5f2205a8', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'DEDE NURYADIN', '3302032212960007', 'BANYUMAS', '1996-12-22', 'L', 'Islam', 'belum_menikah', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEDE', 'Ibu DEDE', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 9');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9e547dd3-56fb-4915-a477-2828093ec4bd', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'FIRNANDA ABDI RICOLA', '3321012002040008', 'DEMAK', '2004-02-20', 'L', 'Islam', 'belum_menikah', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FIRNANDA', 'Ibu FIRNANDA', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 10');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('023c054f-b774-453c-8072-a52f95664a4d', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'ANDREANSYAH', '3302032005030003', 'BANYUMAS', '2003-05-20', 'L', 'Islam', 'belum_menikah', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANDREANSYAH', 'Ibu ANDREANSYAH', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 11');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8c007793-7c1e-42a1-ad79-e01f9c64d3ba', '08d60fbc-eca2-4151-9edc-80f926c50866', 'DANIEL FADILA PUTRA', '3301032404050001', 'CILACAP', '2005-04-24', 'L', 'Islam', 'belum_menikah', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DANIEL', 'Ibu DANIEL', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 12');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('da8d60c9-8599-41e6-845b-8f82c713f9b0', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'ADITIYA RIFQI ANWAR', '3301022701040004', 'CILACAP', '2004-01-27', 'L', 'Islam', 'belum_menikah', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITIYA', 'Ibu ADITIYA', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 13');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('099016ee-f253-41be-a7e5-1131b62a33fb', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'KHOIRUNNISA', '3324185909970001', 'KENDAL', '1997-09-19', 'P', 'Islam', 'belum_menikah', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHOIRUNNISA', 'Ibu KHOIRUNNISA', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 14');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('fe41ba55-abc8-4c4d-a201-cbfafbe8aad7', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'AGUS HERMAWAN', '3307042709010005', 'WONOSOBO', '2001-09-27', 'L', 'Islam', 'belum_menikah', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Wonosobo', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AGUS', 'Ibu AGUS', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 15');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('83b30f26-c69a-4c39-943a-b9a0d32e463d', '08d60fbc-eca2-4151-9edc-80f926c50866', 'ANGGA SANTOSO', '1807211003020004', 'LABUHAN RATU LIMA', '2002-03-10', 'L', 'Islam', 'belum_menikah', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANGGA', 'Ibu ANGGA', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 16');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cac5178b-90cd-4e4a-b024-edb2bd0aed3f', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'FATHUR ROHMAN', '3301091102050003', 'CILACAP', '2005-02-11', 'L', 'Islam', 'belum_menikah', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FATHUR', 'Ibu FATHUR', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 17');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c8cfa626-2b50-4868-af37-0d0a747956f9', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'ITMAMUL WAFA', '3301100411970001', 'CILACAP', '1997-11-04', 'L', 'Islam', 'belum_menikah', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ITMAMUL', 'Ibu ITMAMUL', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 18');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('84e27180-744f-4af2-bcda-ca997d1d84c7', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'DWI WAHYU SETIAWAN', '3301012510990001', 'CILACAP', '1999-10-25', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 19');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('40c29252-8cc5-414c-9779-e3d9d9d2075f', '08d60fbc-eca2-4151-9edc-80f926c50866', 'DEWA ARI SAPUTRA GOEVARA', '3322181411050002', 'KAB. SEMARANG', '2005-11-14', 'L', 'Islam', 'belum_menikah', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEWA', 'Ibu DEWA', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 20');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b7bd7d2e-5725-4618-a03f-e79daca03d77', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'VERDY ARYA IRAWAN', '3301090807050002', 'CILACAP', '2005-07-08', 'L', 'Islam', 'belum_menikah', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah VERDY', 'Ibu VERDY', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 21');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4419f2f4-a5fd-4cc1-b97c-ea3d9fd2d579', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'STEWARD OMEGA BENYAMIN', '3374080608940001', 'SEMARANG', '1994-08-06', 'L', 'Islam', 'belum_menikah', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah STEWARD', 'Ibu STEWARD', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 22');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0ea69733-d6ee-44c8-b445-a2312b5d4d3b', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'MUHAMMAD IRFAN EFENDI', '3315131511000001', 'GRBOGAN', '2000-11-15', 'L', 'Islam', 'belum_menikah', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 23');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4ffebc08-fce7-4f43-8ad2-5afc0a862df1', '08d60fbc-eca2-4151-9edc-80f926c50866', 'FERRY NUR SAPUTRA', '3315182305040003', 'GROBOGAN', '2004-05-23', 'L', 'Islam', 'belum_menikah', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FERRY', 'Ibu FERRY', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 24');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9d05ec76-fc26-40a4-89da-d3a014c03509', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'WAHID MUZANI', '3301012603030004', 'CILACAP', '2003-03-26', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WAHID', 'Ibu WAHID', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 25');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4833501f-9303-4a2d-af34-75ef9465cb07', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'SYAIFUL ANJAR', '1810072601020003', 'WARINGINSARI TIMUR', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SYAIFUL', 'Ibu SYAIFUL', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 26');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e841b2da-db35-4c56-8629-00aa47ec941c', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'FEBRI SETIAWAN', '3301011202990001', 'CILACAP', '1999-02-12', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBRI', 'Ibu FEBRI', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 27');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a5a46eec-14c6-4bd1-b0b6-e763fd70581b', '08d60fbc-eca2-4151-9edc-80f926c50866', 'PERI AKBAR WIBOWO', '3329090808970002', 'BREBES', '1997-08-08', 'L', 'Islam', 'belum_menikah', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Brebes', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PERI', 'Ibu PERI', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 28');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('171fd00d-b974-4625-a270-d5b946db3b01', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'KURNIAWAN FEBRIANSYAH', '3204141602030002', 'BANDUNG', '2003-02-16', 'L', 'Islam', 'belum_menikah', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Bandung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KURNIAWAN', 'Ibu KURNIAWAN', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 29');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2a5c0890-f068-4a4d-91fa-aac02662c1cd', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'RINA HESTIANA', '3308105703040001', 'MAGELANG', '2002-09-15', 'P', 'Islam', 'belum_menikah', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Magelang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RINA', 'Ibu RINA', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 30');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('31a509fd-370b-4f57-a5f0-fa3edde52f09', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'KOLIFAH LISTYANINGRUM', '3374024509980002', 'SEMARANG', '1998-09-05', 'P', 'Islam', 'belum_menikah', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KOLIFAH', 'Ibu KOLIFAH', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 31');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('672b7a13-e155-46ea-b286-635c318efa74', '08d60fbc-eca2-4151-9edc-80f926c50866', 'RIZKI NOVIANA', '3324084211980002', 'KENDAL', '1998-11-02', 'P', 'Islam', 'belum_menikah', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIZKI', 'Ibu RIZKI', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 32');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e2a8649a-2b3d-42bb-8d16-8439302e2649', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'SUTAN MHD AMIN JAMAL', '1302102310980002', 'SALAYO', '1998-10-23', 'L', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SUTAN', 'Ibu SUTAN', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 33');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5cee2537-3193-4843-8645-5d49cde3587c', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'PRAMESSHEILA GITA ANISSA', '3374135804980004', 'KUDUS', '1998-04-18', 'P', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PRAMESSHEILA', 'Ibu PRAMESSHEILA', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 34');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('212fa632-a482-404c-a007-66aadaae9958', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'AMAN BAROKAH', '3215010106050003', 'KARAWANG', '2005-06-01', 'L', 'Islam', 'belum_menikah', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AMAN', 'Ibu AMAN', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 35');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('15c7e545-1bda-4202-a91d-35bc558896f8', '08d60fbc-eca2-4151-9edc-80f926c50866', 'ALIF AL FIAN HIDAYAT', '3215292212050006', 'CIAMIS', '2005-12-22', 'L', 'Islam', 'belum_menikah', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALIF', 'Ibu ALIF', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 36');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ef1ee98c-f33b-47f0-8e50-6260c14cb6a0', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'REYVAL HIDAYAT RHAMADAN', '3215052109050008', 'KARAWANG', '2005-09-21', 'L', 'Islam', 'belum_menikah', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah REYVAL', 'Ibu REYVAL', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 37');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f5c25af1-3fb7-407f-8122-20b0bf9432ac', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'FEBY SUSANTO', '3273121802990003', 'CILACAP', '1999-02-18', 'L', 'Islam', 'belum_menikah', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBY', 'Ibu FEBY', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 38');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3a6c982d-1624-4a44-93f8-701526337b1a', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'SATRIA ADJI NUGROHO', '3301211303020001', 'CILACAP', '2002-03-13', 'L', 'Islam', 'belum_menikah', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SATRIA', 'Ibu SATRIA', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 39');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('99ad1c8d-d4d4-45a2-bc05-b7809ef5189a', '08d60fbc-eca2-4151-9edc-80f926c50866', 'MUHAMAD NIMA ROFIQ ARDIANSAH', '3322072103020001', 'KAB. SEMARANG', '2002-03-21', 'L', 'Islam', 'belum_menikah', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 40');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('eef9e24f-58d0-491f-91a1-68c64546a27a', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'NURSALIM', '3301191003970004', 'CILACAP', '1997-03-10', 'L', 'Islam', 'belum_menikah', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NURSALIM', 'Ibu NURSALIM', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 41');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('14f78664-d1ca-41b9-af7c-47cd07853694', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'ALDHILA KHOIRU AKILA', '3322013007050001', 'KAB. SEMARANG', '2005-07-30', 'L', 'Islam', 'belum_menikah', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDHILA', 'Ibu ALDHILA', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 42');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ecceb299-7c09-42e9-90be-26a141532ca3', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'SAIFUL ISMAN', '3322080202060005', 'KAB. SEMARANG', '2006-02-02', 'L', 'Islam', 'belum_menikah', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SAIFUL', 'Ibu SAIFUL', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 43');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ba23d9d8-fbf8-4847-acec-bae7df6bd5be', '08d60fbc-eca2-4151-9edc-80f926c50866', 'DENIS ADITYA FAHRI', '3322110407040001', 'KAB. SEMARANG', '2004-07-04', 'L', 'Islam', 'belum_menikah', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENIS', 'Ibu DENIS', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 44');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('255e484c-08ab-405b-a062-fc4bb2694268', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'FREDY HARITS WIJANARKO', '3309070310059005', 'BOYOLALI', '2005-10-03', 'L', 'Islam', 'belum_menikah', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Boyolali', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FREDY', 'Ibu FREDY', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 45');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('323445ff-29ed-422c-808f-4cab11b7f665', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'HASAN SIDIK', '3301122003980003', 'CILACAP', '1998-03-20', 'L', 'Islam', 'belum_menikah', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah HASAN', 'Ibu HASAN', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 46');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9e295cdc-cf74-4e09-b559-c3e8159e7f93', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'MUHAMMAD ATOUR ROHMAN', '3324092512040002', 'KENDAL', '2004-12-25', 'L', 'Islam', 'belum_menikah', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 47');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b56d7572-a5da-4309-ae6a-5a96bd284a57', '08d60fbc-eca2-4151-9edc-80f926c50866', 'RONALD MAULANA SAPUTRA', '3215052303060002', 'KARAWANG', '2006-03-23', 'L', 'Islam', 'belum_menikah', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RONALD', 'Ibu RONALD', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 48');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5da96e8c-279a-4889-acf5-ac8f6422d964', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'ALWI AWALUL BANI', '3215052912050005', 'TASIKMALAYA', '2005-12-29', 'L', 'Islam', 'belum_menikah', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALWI', 'Ibu ALWI', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 49');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a48598f6-859d-4a30-a7b2-23b749a6159b', '336c678c-096d-4297-aeb0-ecd4c86f140b', 'GIN FANDIKA LESMANA', '3215050102050010', 'KARAWANG', '2005-02-01', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GIN', 'Ibu GIN', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 50');

-- Insert Penempatan
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d7793c6f-53fe-4f4a-9021-19f0e18021ef', '2eb8c87c-7c77-4d91-ac52-3e6912ae5ae9', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4ffbeae9-4ef6-4d0c-8ccc-3e5d3b58d24d', 'eb05889f-a905-4cfd-afe7-074387d7f2ca', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('36a3db96-d8d8-4eb0-b8a8-8e9708b0b200', '4cdba98e-8ba8-4354-b809-1a8a5f2205a8', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4b94bc8f-b8f9-42f3-b897-27c7835686a6', '8c007793-7c1e-42a1-ad79-e01f9c64d3ba', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('015e3c6b-161a-4aa2-8ede-8a0798843ebb', 'fe41ba55-abc8-4c4d-a201-cbfafbe8aad7', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6c8cca82-293a-4399-9f65-98bd23494d5e', 'c8cfa626-2b50-4868-af37-0d0a747956f9', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('63dfdf15-938f-4233-8fe1-5c780db4ee82', 'b7bd7d2e-5725-4618-a03f-e79daca03d77', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d299e62f-3bc9-4f50-975b-1d21129eea11', '4ffebc08-fce7-4f43-8ad2-5afc0a862df1', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8bbb57df-1c0e-4cbb-8ed6-0c09016aa79b', 'e841b2da-db35-4c56-8629-00aa47ec941c', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('474b58b5-cb57-45e3-baa7-d1e1fb9d3f8b', '2a5c0890-f068-4a4d-91fa-aac02662c1cd', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('0dfe3fca-d4fc-4f3e-bed1-145fc7627099', 'e2a8649a-2b3d-42bb-8d16-8439302e2649', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d782d55b-739f-4868-b9d7-5c34f22be1ac', '15c7e545-1bda-4202-a91d-35bc558896f8', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4466b7e2-29ca-41d1-9137-25724cb354d8', '3a6c982d-1624-4a44-93f8-701526337b1a', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('779f3918-22af-4abf-ab05-39393cb1cc2d', '14f78664-d1ca-41b9-af7c-47cd07853694', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('44edf21f-5199-4520-a821-d1ddf57d9555', '255e484c-08ab-405b-a062-fc4bb2694268', '9e24d6d9-e666-460c-9061-c5680885e690', 'e160f601-b0f0-43f2-abbb-40f210c5502d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6d275f2c-c97d-4f31-b28b-92656abd6414', 'b56d7572-a5da-4309-ae6a-5a96bd284a57', 'd931b7b5-9122-4620-bb4c-ebfc0aef66f4', 'a358fe5e-d54b-4d2f-9e57-78fc5fb9c9b7', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- ✅ Seed data import completed!

-- FULL Seed Data Dashboard Magang Jepang - Yutaka LPK
-- Generated: 2025-07-11T04:43:39.131Z
-- Total Records: <PERSON><PERSON>(3), <PERSON><PERSON><PERSON>(3), <PERSON><PERSON><PERSON><PERSON>(4), <PERSON><PERSON><PERSON>(170), <PERSON><PERSON><PERSON><PERSON>(110)

-- Disable foreign key checks
SET session_replication_role = replica;

-- Clear existing data (uncomment if needed)
-- TRUNCATE TABLE penempatan_siswa CASCADE;
-- TRUNCATE TABLE siswa CASCADE;
-- TRUNCATE TABLE perusahaan_penerima CASCADE;
-- TRUNCATE TABLE lpk_mitra CASCADE;
-- TRUNCATE TABLE kumiai CASCADE;

-- Insert LPK Mitra (3 records)
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('98d0deb4-4b90-4315-8361-d37abf6e88bf', '<PERSON><PERSON><PERSON>', 'Jl. Raya Utama No. 123, Jakarta Pusat 10110', 'Jakarta', 'DKI Jakarta', 'Bapak Yutaka Tanaka', 'Ibu Sari Wijaya', '021-1234-5678', '<EMAIL>', 'https://yutaka.co.id', 'aktif', '2024-01-01', 'LPK Yutaka - Partner utama untuk program magang Jepang');
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'LPK Dummy', 'Jl. Pendidikan No. 456, Bandung 40123', 'Bandung', 'Jawa Barat', 'Bapak Pimpinan LPK', 'Ibu Kontak Person', '022-9876-5432', '<EMAIL>', 'https://lpkdummy.co.id', 'aktif', '2024-01-01', 'LPK Dummy untuk testing dan development');
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('000c963d-94bb-47e3-906b-9f21f7b5262d', 'LPK Central Java', 'Jl. Pemuda No. 789, Semarang 50132', 'Semarang', 'Jawa Tengah', 'Bapak Direktur Central', 'Ibu Manager Central', '024-1111-2222', '<EMAIL>', 'https://lpkcentral.co.id', 'aktif', '2024-01-01', 'LPK Central Java untuk wilayah Jawa Tengah');

-- Insert Kumiai (3 records)
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('993a5bea-db58-4b06-83b7-4b93dd10ce8f', 'Gokei Cloud Kyodo Kumiai', 'GOKEI', '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002', 'Tokyo', 'Tokyo', 'Tanaka San', '+81-3-1234-5678', '<EMAIL>', 'https://gokei.jp', 'aktif', 'Kumiai utama untuk program magang');
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('c54acdd5-0363-4812-a2de-89875f053111', 'TIC Kyodo Kumiai', 'TIC', '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022', 'Tokyo', 'Tokyo', 'Yamada San', '+81-3-2345-6789', '<EMAIL>', 'https://tic.jp', 'aktif', 'Kumiai untuk bidang teknik');
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', 'Osaka Workers Kumiai', 'OWK', '3-3-3 Namba, Chuo-ku, Osaka 542-0076', 'Osaka', 'Osaka', 'Suzuki San', '+81-6-3456-7890', '<EMAIL>', 'https://owk.jp', 'aktif', 'Kumiai untuk wilayah Osaka');

-- Insert Perusahaan (4 records)
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', 'Tokyo Manufacturing Co., Ltd.', '4-4-4 Minato, Minato-ku, Tokyo 105-0003', 'Tokyo', 'Tokyo', 'Manufacturing', 'Sato San', '+81-3-4567-8901', '<EMAIL>', 'https://tokyo-mfg.jp', 'aktif', 'Perusahaan manufaktur terkemuka di Tokyo');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', 'Osaka Technical Industries', '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', 'Osaka', 'Osaka', 'Technical Services', 'Watanabe San', '+81-6-5678-9012', '<EMAIL>', 'https://osaka-tech.jp', 'aktif', 'Perusahaan teknik di Osaka');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', 'Nagoya Automotive Parts', '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', 'Nagoya', 'Aichi', 'Automotive', 'Takahashi San', '+81-52-6789-0123', '<EMAIL>', 'https://nagoya-auto.jp', 'aktif', 'Perusahaan otomotif di Nagoya');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', 'Kyoto Electronics Corp.', '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', 'Kyoto', 'Kyoto', 'Electronics', 'Nakamura San', '+81-75-7890-1234', '<EMAIL>', 'https://kyoto-elec.jp', 'aktif', 'Perusahaan elektronik di Kyoto');

-- Insert Siswa (170 records)
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('fd804697-808a-48e4-aa6d-d6179ab8d02a', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'GULAM FATHUR RISQI', '3301092611020001', 'CILACAP', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GULAM', 'Ibu GULAM', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ded9b960-f721-4abe-8c28-8ace1d25edd4', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ADITYA HERI PURNOMO', '3301240603030001', 'CILACAP', '2003-03-06', 'L', 'Islam', 'belum_menikah', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITYA', 'Ibu ADITYA', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ce16aa9f-28c7-4864-a9d7-aa668f2d440f', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'BRIYAN BINTORO', '3403010607990003', 'GUNUNG KIDUL', '1999-07-06', 'L', 'Islam', 'belum_menikah', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Gunung Kidul', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BRIYAN', 'Ibu BRIYAN', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('166f7eea-d4c3-4271-978a-2fd6fa4dced6', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'RIO DWI SATRIOTOMO', '3322031212030002', 'KAB. SEMARANG', '1905-06-25', 'L', 'Islam', 'belum_menikah', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIO', 'Ibu RIO', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('801a22f3-5948-4337-aa05-500f6b375f41', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'DIMAS WAHYU LUWANGGA PRASETYA', '3322081912000001', 'KAB. SEMARANG', '2000-12-19', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DIMAS', 'Ibu DIMAS', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2091c33a-bd8c-42c8-926e-121010848182', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'MIRZA ALI HILMI', '3328111510020004', 'Tegal', '2002-10-15', 'L', 'Islam', 'belum_menikah', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Tegal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MIRZA', 'Ibu MIRZA', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f2cdac08-7503-4ee7-8b45-dec950827ef5', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'LANGGA PRATAMA', '3301090701050001', 'Cilacap', '2005-01-07', 'L', 'Islam', 'belum_menikah', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LANGGA', 'Ibu LANGGA', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6a1c473e-bc58-43c3-b7af-0fab062e4eec', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'ADI NUR MUHAMMAD SAPUTRA', '3324110111010003', 'KENDAL', '2001-11-01', 'L', 'Islam', 'belum_menikah', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADI', 'Ibu ADI', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('303e1f3d-de70-4028-bde5-dd837b86c254', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DEDE NURYADIN', '3302032212960007', 'BANYUMAS', '1996-12-22', 'L', 'Islam', 'belum_menikah', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEDE', 'Ibu DEDE', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('55cdfe95-d094-499b-a6d1-e36e66e2c36c', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FIRNANDA ABDI RICOLA', '3321012002040008', 'DEMAK', '2004-02-20', 'L', 'Islam', 'belum_menikah', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FIRNANDA', 'Ibu FIRNANDA', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0240c4fa-fe58-462b-bffe-f5116e39c5a6', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ANDREANSYAH', '3302032005030003', 'BANYUMAS', '2003-05-20', 'L', 'Islam', 'belum_menikah', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANDREANSYAH', 'Ibu ANDREANSYAH', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1ec891d2-6825-4d9f-be43-f84cfd21a90f', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DANIEL FADILA PUTRA', '3301032404050001', 'CILACAP', '2005-04-24', 'L', 'Islam', 'belum_menikah', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DANIEL', 'Ibu DANIEL', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('be82a581-a4fb-4bbd-914a-199ab1991636', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ADITIYA RIFQI ANWAR', '3301022701040004', 'CILACAP', '2004-01-27', 'L', 'Islam', 'belum_menikah', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITIYA', 'Ibu ADITIYA', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f56067e8-0891-416d-945d-19ceb1856f78', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'KHOIRUNNISA', '3324185909970001', 'KENDAL', '1997-09-19', 'P', 'Islam', 'belum_menikah', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHOIRUNNISA', 'Ibu KHOIRUNNISA', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('59c919cd-9bfa-4ef5-b1b5-dbfcf58dc7ba', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'AGUS HERMAWAN', '3307042709010005', 'WONOSOBO', '2001-09-27', 'L', 'Islam', 'belum_menikah', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Wonosobo', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AGUS', 'Ibu AGUS', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e5ea124d-5fb9-45e7-ade8-947b43fd2685', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'ANGGA SANTOSO', '1807211003020004', 'LABUHAN RATU LIMA', '2002-03-10', 'L', 'Islam', 'belum_menikah', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANGGA', 'Ibu ANGGA', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a4b14054-2194-466d-a1d4-fd50a5955cf7', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FATHUR ROHMAN', '3301091102050003', 'CILACAP', '2005-02-11', 'L', 'Islam', 'belum_menikah', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FATHUR', 'Ibu FATHUR', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2bd2e84d-8c15-4a84-8f38-6ae4e9de8fea', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ITMAMUL WAFA', '3301100411970001', 'CILACAP', '1997-11-04', 'L', 'Islam', 'belum_menikah', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ITMAMUL', 'Ibu ITMAMUL', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8de6e7e0-c06b-4491-a998-e53b287af938', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'DWI WAHYU SETIAWAN', '3301012510990001', 'CILACAP', '1999-10-25', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3fcc0772-c1e9-4f8f-85a6-d22cd362e31a', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'DEWA ARI SAPUTRA GOEVARA', '3322181411050002', 'KAB. SEMARANG', '2005-11-14', 'L', 'Islam', 'belum_menikah', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEWA', 'Ibu DEWA', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5b520abe-3b8e-402a-b770-eb6febeb2c46', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'VERDY ARYA IRAWAN', '3301090807050002', 'CILACAP', '2005-07-08', 'L', 'Islam', 'belum_menikah', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah VERDY', 'Ibu VERDY', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0540a273-3e28-40a2-9e10-382766fd4df8', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'STEWARD OMEGA BENYAMIN', '3374080608940001', 'SEMARANG', '1994-08-06', 'L', 'Islam', 'belum_menikah', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah STEWARD', 'Ibu STEWARD', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('02923ade-6b0d-497e-a95a-15c12d54fd12', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUHAMMAD IRFAN EFENDI', '3315131511000001', 'GRBOGAN', '2000-11-15', 'L', 'Islam', 'belum_menikah', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('fdd82101-956e-4e0c-a5d4-e06d54b35a43', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'FERRY NUR SAPUTRA', '3315182305040003', 'GROBOGAN', '2004-05-23', 'L', 'Islam', 'belum_menikah', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FERRY', 'Ibu FERRY', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b0f48966-c057-4701-a274-e0ceb9bbb714', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'WAHID MUZANI', '3301012603030004', 'CILACAP', '2003-03-26', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WAHID', 'Ibu WAHID', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('faeda35d-910b-42e8-9c39-f34ddf8c6c2b', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'SYAIFUL ANJAR', '1810072601020003', 'WARINGINSARI TIMUR', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SYAIFUL', 'Ibu SYAIFUL', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('dd206734-a298-4285-861c-80cf4ba0b107', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'FEBRI SETIAWAN', '3301011202990001', 'CILACAP', '1999-02-12', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBRI', 'Ibu FEBRI', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4f3633e6-c4fa-461c-80ac-e6771fd7ed4e', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'PERI AKBAR WIBOWO', '3329090808970002', 'BREBES', '1997-08-08', 'L', 'Islam', 'belum_menikah', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Brebes', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PERI', 'Ibu PERI', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('854b9504-4ed4-492b-8b51-5b888b3f26d9', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'KURNIAWAN FEBRIANSYAH', '3204141602030002', 'BANDUNG', '2003-02-16', 'L', 'Islam', 'belum_menikah', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Bandung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KURNIAWAN', 'Ibu KURNIAWAN', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('60f76f7d-94da-469d-a1ad-6cbbb0ecc3e6', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'RINA HESTIANA', '3308105703040001', 'MAGELANG', '2002-09-15', 'P', 'Islam', 'belum_menikah', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Magelang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RINA', 'Ibu RINA', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5d01d407-5fa0-4424-8ab0-8116b36b1f5e', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'KOLIFAH LISTYANINGRUM', '3374024509980002', 'SEMARANG', '1998-09-05', 'P', 'Islam', 'belum_menikah', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KOLIFAH', 'Ibu KOLIFAH', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c6900713-70a9-4365-a566-05fc5ef0568d', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'RIZKI NOVIANA', '3324084211980002', 'KENDAL', '1998-11-02', 'P', 'Islam', 'belum_menikah', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIZKI', 'Ibu RIZKI', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('454553b1-ef92-48fd-abc1-9f9ea115c57c', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'SUTAN MHD AMIN JAMAL', '1302102310980002', 'SALAYO', '1998-10-23', 'L', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SUTAN', 'Ibu SUTAN', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2347d962-ceb0-42f1-ae8b-598adab24cda', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'PRAMESSHEILA GITA ANISSA', '3374135804980004', 'KUDUS', '1998-04-18', 'P', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PRAMESSHEILA', 'Ibu PRAMESSHEILA', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3b8b368d-cdc4-4e57-9372-06a305ab74e2', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AMAN BAROKAH', '3215010106050003', 'KARAWANG', '2005-06-01', 'L', 'Islam', 'belum_menikah', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AMAN', 'Ibu AMAN', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('82e5afdc-edba-45fc-bdfa-8e615ba798dc', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ALIF AL FIAN HIDAYAT', '3215292212050006', 'CIAMIS', '2005-12-22', 'L', 'Islam', 'belum_menikah', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALIF', 'Ibu ALIF', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('45acd3db-e5bf-4cea-a619-497371708c56', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'REYVAL HIDAYAT RHAMADAN', '3215052109050008', 'KARAWANG', '2005-09-21', 'L', 'Islam', 'belum_menikah', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah REYVAL', 'Ibu REYVAL', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('190dc968-920c-448a-98a4-467ec594f2a8', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FEBY SUSANTO', '3273121802990003', 'CILACAP', '1999-02-18', 'L', 'Islam', 'belum_menikah', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBY', 'Ibu FEBY', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2532449b-eb67-45b0-b694-1366f05d89ae', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'SATRIA ADJI NUGROHO', '3301211303020001', 'CILACAP', '2002-03-13', 'L', 'Islam', 'belum_menikah', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SATRIA', 'Ibu SATRIA', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8292b88f-1f18-47a8-91bd-cd417eb780ee', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'MUHAMAD NIMA ROFIQ ARDIANSAH', '3322072103020001', 'KAB. SEMARANG', '2002-03-21', 'L', 'Islam', 'belum_menikah', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c8553f50-717e-4d04-8660-c935d2896c15', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'NURSALIM', '3301191003970004', 'CILACAP', '1997-03-10', 'L', 'Islam', 'belum_menikah', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NURSALIM', 'Ibu NURSALIM', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('64a1f604-fc02-4097-87c3-722c4ce8dd1e', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ALDHILA KHOIRU AKILA', '3322013007050001', 'KAB. SEMARANG', '2005-07-30', 'L', 'Islam', 'belum_menikah', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDHILA', 'Ibu ALDHILA', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4c7614ed-1f3a-423e-8b28-be9f1942af1e', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'SAIFUL ISMAN', '3322080202060005', 'KAB. SEMARANG', '2006-02-02', 'L', 'Islam', 'belum_menikah', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SAIFUL', 'Ibu SAIFUL', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7b1b3190-6d15-4e66-bea3-eb6e0e7a13f5', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'DENIS ADITYA FAHRI', '3322110407040001', 'KAB. SEMARANG', '2004-07-04', 'L', 'Islam', 'belum_menikah', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENIS', 'Ibu DENIS', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2ed2c1b0-3e58-49bc-9e4f-5655a56a75f4', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'FREDY HARITS WIJANARKO', '3309070310059005', 'BOYOLALI', '2005-10-03', 'L', 'Islam', 'belum_menikah', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Boyolali', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FREDY', 'Ibu FREDY', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c06e5f94-1e52-4f69-b49f-149c17d230ae', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'HASAN SIDIK', '3301122003980003', 'CILACAP', '1998-03-20', 'L', 'Islam', 'belum_menikah', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah HASAN', 'Ibu HASAN', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cf3a80c0-9e43-4dab-9576-939fd231ce04', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUHAMMAD ATOUR ROHMAN', '3324092512040002', 'KENDAL', '2004-12-25', 'L', 'Islam', 'belum_menikah', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('58dcb08f-e97c-40ae-9105-ce5b6c76e3b3', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'RONALD MAULANA SAPUTRA', '3215052303060002', 'KARAWANG', '2006-03-23', 'L', 'Islam', 'belum_menikah', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RONALD', 'Ibu RONALD', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8645918b-2aae-46b2-899b-149967ca3cdc', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ALWI AWALUL BANI', '3215052912050005', 'TASIKMALAYA', '2005-12-29', 'L', 'Islam', 'belum_menikah', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALWI', 'Ibu ALWI', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3c70a728-ddd2-4d2d-be4c-28bbc34464d0', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'GIN FANDIKA LESMANA', '3215050102050010', 'KARAWANG', '2005-02-01', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GIN', 'Ibu GIN', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e75c44c6-3756-4b58-ad2c-05181f409b78', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DENDRA JULPIKAR', '3215062001060002', 'KARAWANG', '2006-01-20', 'L', 'Islam', 'belum_menikah', 'CIKANGKUNG BARAT II RT. 010 RW. 002 KEL. RENGASDENGKLOK UTARA KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENDRA', 'Ibu DENDRA', 'CIKANGKUNG BARAT II RT. 010 RW. 002 KEL. RENGASDENGKLOK UTARA KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d5e480bf-7dfc-4d82-9d98-eda3f09ba385', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'AHMAD', '3215271806050001', 'KARAWANG', '2005-06-18', 'L', 'Islam', 'belum_menikah', 'LEUWEUNG KAUNG RT. 008 RW. 004 KEL. MEKARMULYA KEC. TELUKJAMBE BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'LEUWEUNG KAUNG RT. 008 RW. 004 KEL. MEKARMULYA KEC. TELUKJAMBE BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('84f9e7c0-4ad4-4d31-a78b-467c2488ee2b', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'OKSAY GILANG RAMADHAN', '3215053010030002', 'KARAWANG', '2004-10-30', 'L', 'Islam', 'belum_menikah', 'PERUM PURI KOSAMBI BLOK G NO 29 RT. 056 RW. 016 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah OKSAY', 'Ibu OKSAY', 'PERUM PURI KOSAMBI BLOK G NO 29 RT. 056 RW. 016 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('817853d8-fafc-4b3e-b148-b01ffa960cf4', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DEWA SAPUTRA', '3322101903040002', 'KAB. SEMARANG', '2004-03-19', 'L', 'Islam', 'belum_menikah', 'PANDEAN RT. 005 RW. 001 KEL. LODOYONG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEWA', 'Ibu DEWA', 'PANDEAN RT. 005 RW. 001 KEL. LODOYONG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('12383f62-2ba7-4e6c-bf15-598c8eb010d3', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUHAMMAD SUALMAN', '3324092904020002', 'KENDAL', '2002-04-29', 'L', 'Islam', 'belum_menikah', 'KRAYAPAN RT. 003 RW. 4 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'KRAYAPAN RT. 003 RW. 4 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('200f4845-a595-4370-bd2f-1c820edfa9dc', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'FAHRI CAHYADI', '3215061802060001', 'KARAWANG', '2006-02-18', 'L', 'Islam', 'belum_menikah', 'DUSUN PACING UTARA RT. 007 RW. 003 KEL. DEWISARI KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAHRI', 'Ibu FAHRI', 'DUSUN PACING UTARA RT. 007 RW. 003 KEL. DEWISARI KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a3229a08-f2ee-48d5-8d21-26d2332e3264', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'GALERIUS MIKAEL PORAWOUW', '3275030806050004', 'BEKASI', '2005-06-08', 'L', 'Islam', 'belum_menikah', 'PRIMA HARAPAN REGENCY L12/21 RT. 001 RW. 012 KEL. HARAPANBARU KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Bekasi', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GALERIUS', 'Ibu GALERIUS', 'PRIMA HARAPAN REGENCY L12/21 RT. 001 RW. 012 KEL. HARAPANBARU KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('17cc4d60-db1c-4529-a55c-7aacad7d4e73', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'NURMANSYAH', '3215011811050003', 'KARAWANG', '2005-11-18', 'L', 'Islam', 'belum_menikah', 'PASIR JENGKOL RT. 007 RW. 013 KEL. TANJUNGPURA KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NURMANSYAH', 'Ibu NURMANSYAH', 'PASIR JENGKOL RT. 007 RW. 013 KEL. TANJUNGPURA KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('26d22384-2363-4946-afe8-2e2b2aeb8bc8', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MICO RAMADHAN', '3172041711011005', 'JAKARTA', '2001-11-17', 'L', 'Islam', 'belum_menikah', 'JL. KAYU TINGGI KP.KANDANG SAPI RT. 002 RW. 006 KEL. CAKUNG TIMUR KEC. CAKUNG JAKARTA TIMUR DKI JAKARTA', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MICO', 'Ibu MICO', 'JL. KAYU TINGGI KP.KANDANG SAPI RT. 002 RW. 006 KEL. CAKUNG TIMUR KEC. CAKUNG JAKARTA TIMUR DKI JAKARTA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f32df7b3-8de6-4a59-8b08-10f56e3ea3ee', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'MUHAMMAD FATKHUR ROZI', '3275032903050006', 'BEKASI', '2005-03-29', 'L', 'Islam', 'belum_menikah', 'KAV.KABEL MAS RT. 002 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Bekasi', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'KAV.KABEL MAS RT. 002 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('659f76ee-a55d-414e-ad97-28fa3fa9d9c9', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'TUBAGUS MUHAMMAD FADIL HASBI', '3215051007050002', 'KARAWANG', '2005-07-10', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN I RT. 004 RW. 001 KEL. GINTUNGKERTA KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah TUBAGUS', 'Ibu TUBAGUS', 'DUSUN KRAJAN I RT. 004 RW. 001 KEL. GINTUNGKERTA KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cc795350-ea84-4883-817e-335daa71d3f1', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'EVAN MAULANA WIDAYAT', '3374143101050001', 'KENDAL', '2005-01-31', 'L', 'Islam', 'belum_menikah', 'NGADIPIRO RT. 002 RW. 010 KEL. KERTOSARI KEC. SINGOROJO KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah EVAN', 'Ibu EVAN', 'NGADIPIRO RT. 002 RW. 010 KEL. KERTOSARI KEC. SINGOROJO KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6b7d0293-4bce-4b1a-b166-996b79c0e66b', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ILHAM ALFATTA HAFID ITTAQULLAHA', '3322010309050001', 'KAB.SEMARANG', '2005-09-03', 'L', 'Islam', 'belum_menikah', 'GEDAD RT. 009 RW. 001 KEL. WATES KEC. GETASAN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ILHAM', 'Ibu ILHAM', 'GEDAD RT. 009 RW. 001 KEL. WATES KEC. GETASAN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1d8cd2d7-cc97-4eba-a1d6-f6ab7e1f62b2', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'MOHAMMAD IRFAN CHARISUDDIN', '3324192705020003', 'KENDAL', '2002-05-27', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 001 RW. 001 KEL. NGAMPEL KULON KEC. NGAMPEL KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MOHAMMAD', 'Ibu MOHAMMAD', 'KRAJAN RT. 001 RW. 001 KEL. NGAMPEL KULON KEC. NGAMPEL KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('34bc6573-3232-4821-9b5b-4e7aa128497b', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MAHBUB KHASAN FAUZI', '3323070409020002', 'TEMANGGUNG', '2002-08-27', 'L', 'Islam', 'belum_menikah', 'KAUMAN RT. 006 RW. 002 KEL. GONDANGWAYANG KEC. KEDU KAB. TEMANGGUNG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Temanggung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MAHBUB', 'Ibu MAHBUB', 'KAUMAN RT. 006 RW. 002 KEL. GONDANGWAYANG KEC. KEDU KAB. TEMANGGUNG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d4ff49e2-4a2b-4b99-94be-bb30577de1f4', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'MUHAMAD RIJAL ARDI ROHMAN', '3324112409030001', 'KENDAL', '2003-09-24', 'L', 'Islam', 'belum_menikah', 'JOHOREJO RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'JOHOREJO RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b16caea0-5994-459d-9b2b-d27ddd26725f', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AHMAD MUSTAFIQI', '3324110105990002', 'KENDAL', '1999-05-01', 'L', 'Islam', 'belum_menikah', 'GG PANCASILA NAMPUROTO RT. 002 RW. 003 KEL. PUCANGREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'GG PANCASILA NAMPUROTO RT. 002 RW. 003 KEL. PUCANGREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0802e511-143f-4ef6-a3ea-15afd037902f', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'DIMAS ANDRIYANTO', '3315180506000001', 'GROBOGAN', '2000-09-22', 'L', 'Islam', 'belum_menikah', 'DUSUN MLANGI RT. 003 RW. 003 KEL. TAJEMSARI KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DIMAS', 'Ibu DIMAS', 'DUSUN MLANGI RT. 003 RW. 003 KEL. TAJEMSARI KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('547f7032-11f0-4a7b-b8fe-2c27f387d085', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ERVINA NUR ALIFIAH', '3215266401040003', 'BOYOLALI', '2004-01-24', 'P', 'Islam', 'belum_menikah', 'SARI INDAH RT. 004 RW. 019 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ERVINA', 'Ibu ERVINA', 'SARI INDAH RT. 004 RW. 019 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5df87fca-5b81-493b-9812-335cd6721881', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'THIO ANSYAHRI', '3214042606940004', 'PURWAKARTA', '1994-06-26', 'L', 'Islam', 'belum_menikah', 'KP ANJUN RT. 003 RW. 001 KEL. ANJUN KEC. PLERED KAB. PURWAKARTA JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Purwakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah THIO', 'Ibu THIO', 'KP ANJUN RT. 003 RW. 001 KEL. ANJUN KEC. PLERED KAB. PURWAKARTA JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('043c16b8-f58c-44a8-a5d5-d2d240159bc6', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'SISKA WIDYANENGSIH', '3215236909010003', 'KARAWANG', '2001-09-29', 'P', 'Islam', 'belum_menikah', 'PULOPUTRI RT. 007 RW. 003 KEL. SUKAMULYA KEC. CILAMAYA KULON KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SISKA', 'Ibu SISKA', 'PULOPUTRI RT. 007 RW. 003 KEL. SUKAMULYA KEC. CILAMAYA KULON KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c45cd002-ae88-4f9b-8d1e-67a43360845c', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'YUMRORTUN NAFISATUR ROHMANIYAH', '3315037112010002', 'GROBOGAN', '2001-12-31', 'P', 'Islam', 'belum_menikah', 'DUSUN KETOPO RT. 003 RW. 004 KEL. KARANGWADER KEC. PENAWANGAN KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah YUMRORTUN', 'Ibu YUMRORTUN', 'DUSUN KETOPO RT. 003 RW. 004 KEL. KARANGWADER KEC. PENAWANGAN KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('30f5011d-1604-4aea-b1a1-e4b677c5a319', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'NISVIATIN NASIKHAH', '3301027110010003', 'CILACAP', '2001-10-31', 'P', 'Islam', 'belum_menikah', 'DONDONG RT. 002 RW. 004 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NISVIATIN', 'Ibu NISVIATIN', 'DONDONG RT. 002 RW. 004 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4e7a11a3-f520-4a87-8eda-5ee4762ed08b', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'DWI KARTINI SARASWATI', '332102610499000', 'DEMAK', '1999-04-21', 'P', 'Islam', 'belum_menikah', 'BILO KAUMAN RT. 002 RW. 008 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', 'BILO KAUMAN RT. 002 RW. 008 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ec48a490-1c59-454e-baee-d7be4e820be0', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ANINDA SHENDIATI MIRABIA', '3321025304020002', 'DEMAK', '2002-04-13', 'P', 'Islam', 'belum_menikah', 'SAMBI RT. 001 RW. 001 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANINDA', 'Ibu ANINDA', 'SAMBI RT. 001 RW. 001 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ad887934-ed0e-4b28-b2d4-69cbd75ca052', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'DEPRI SAPUTRA', '1807162912040001', 'RAJABASA BARU', '2004-12-29', 'L', 'Islam', 'belum_menikah', 'DUSUN VI RT. 030 RW. 012 KEL. RAJABASA BARU KEC. MATARAM BARU KAB. LAMPUNG TIMUR LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung Timur', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEPRI', 'Ibu DEPRI', 'DUSUN VI RT. 030 RW. 012 KEL. RAJABASA BARU KEC. MATARAM BARU KAB. LAMPUNG TIMUR LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('86b7eb9b-87e7-45dd-b264-066b45588b78', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MISBAKHUL HUDA', '3521133112990001', 'NGAWI', '1999-12-31', 'L', 'Islam', 'belum_menikah', 'BELAKANG LAPAS RT. 026 RW. 001 KEL. FATUKBOT KEC. ATAMBUA SELATAN KAB. BELU NUSA TENGGARA TIMUR', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Belu', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MISBAKHUL', 'Ibu MISBAKHUL', 'BELAKANG LAPAS RT. 026 RW. 001 KEL. FATUKBOT KEC. ATAMBUA SELATAN KAB. BELU NUSA TENGGARA TIMUR', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('87ee7ab4-ba21-4fb1-8255-1e3ff3ca8c85', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'MUHAMAD PADLI HAMDANI', '3324111701030001', 'KUNINGAN', '2003-01-17', 'L', 'Islam', 'belum_menikah', 'GEMUHBLANTEN RT. 001 RW. 002 KEL. GEMUHBLANTEN KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'GEMUHBLANTEN RT. 001 RW. 002 KEL. GEMUHBLANTEN KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e6e4b853-9ece-47df-a0d0-d2c3256afda1', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'BUDI ASTUTI', '3305246307040001', 'KEBUMEN', '2006-02-01', 'P', 'Islam', 'belum_menikah', 'KRAJAN II RT. 002 RW. 001 KEL. PEJENGKOLAN KEC. PADURESO Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BUDI', 'Ibu BUDI', 'KRAJAN II RT. 002 RW. 001 KEL. PEJENGKOLAN KEC. PADURESO Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('44b5123d-0340-45cb-bda8-ae1c3c4a4c20', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'DAVANSA EGA WIDYA PRATIWI', '3323085802060004', 'TEMANGGUNG', '2006-02-18', 'P', 'Islam', 'belum_menikah', 'JETIS LOR RT. 002 RW. 003 KEL. PARAKAN KAUMAN KEC. PARAKAN Kab. Temanggung JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DAVANSA', 'Ibu DAVANSA', 'JETIS LOR RT. 002 RW. 003 KEL. PARAKAN KAUMAN KEC. PARAKAN Kab. Temanggung JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6e026dbc-710b-49ec-a76a-c7227433441a', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'HERLINA UCKYA NINGSIH', '3301244112020001', 'CILACAP', '2002-12-01', 'P', 'Islam', 'belum_menikah', 'DUSUN KARANG JAYA RT. 003 RW. 002 KEL. UJUNGGAGAK KEC. KAMPUNG LAUT Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah HERLINA', 'Ibu HERLINA', 'DUSUN KARANG JAYA RT. 003 RW. 002 KEL. UJUNGGAGAK KEC. KAMPUNG LAUT Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1c83a931-b854-4d31-afee-2a5853999be1', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'LULU DWI OKTARIANI', '3301235110030001', 'CILACAP', '2003-10-11', 'P', 'Islam', 'belum_menikah', 'JL. MAHONI NO. 71 RT. 002 RW. 002 KEL. TRITIH KULON KEC. CILACAP UTARA Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LULU', 'Ibu LULU', 'JL. MAHONI NO. 71 RT. 002 RW. 002 KEL. TRITIH KULON KEC. CILACAP UTARA Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('61d6e45f-72fd-42b2-902d-baa739611b12', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'DANAR MAULANA KUSUMA', '3215051602060004', 'KARAWANG', '2006-02-16', 'L', 'Islam', 'belum_menikah', 'JLN.NAGASARI DALAM RT. 003 RW. 002 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DANAR', 'Ibu DANAR', 'JLN.NAGASARI DALAM RT. 003 RW. 002 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e6a2e7cf-82f1-4e38-b266-63c97e53027a', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DYO FIRMAN RAHMADIANSYAH', '3275111106050003', 'BEKASI', '2005-06-11', 'L', 'Islam', 'belum_menikah', 'PERUM BMI 2 BLOK B3 NO 21 RT. 002 RW. 011 KEL. DAWUAN BARAT KEC. CIKAMPEK Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DYO', 'Ibu DYO', 'PERUM BMI 2 BLOK B3 NO 21 RT. 002 RW. 011 KEL. DAWUAN BARAT KEC. CIKAMPEK Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d79365f5-1359-4215-adac-3dd7e9848f01', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUHAMAD SALAHUDIN RIYADH', '3275031809040011', 'BEKASI', '2004-09-18', 'L', 'Islam', 'belum_menikah', 'KAV. KABEL MAS RT. 008 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA Kota Bekasi JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'KAV. KABEL MAS RT. 008 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA Kota Bekasi JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('225c264d-7d6b-44ec-b6d4-9d192e9324f9', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ROBY YANSYAH', '3215102408050002', 'KARAWANG', '2005-08-24', 'L', 'Islam', 'belum_menikah', 'DUSUN PEDES 1 RT. 002 RW. 001 KEL. PAYUNGSARI KEC. PEDES Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ROBY', 'Ibu ROBY', 'DUSUN PEDES 1 RT. 002 RW. 001 KEL. PAYUNGSARI KEC. PEDES Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b5b3ab28-1260-4e70-b594-29df3208c903', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ISMAIL KHAMKAH', '3305031310020002', 'KEBUMEN', '2002-10-13', 'L', 'Islam', 'belum_menikah', 'PLALANGAN KULON RT. 004 RW. 002 KEL. PURWOSARI KEC. PURING Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kebumen', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ISMAIL', 'Ibu ISMAIL', 'PLALANGAN KULON RT. 004 RW. 002 KEL. PURWOSARI KEC. PURING Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('03e9dc01-a80f-4edf-9878-179da9b79dba', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'MUHAMMAD SAHIL AKBAR', '3305122301050004', 'KEBUMEN', '2005-01-23', 'L', 'Islam', 'belum_menikah', 'KEMITIR RT. 003 RW. 001 KEL. BUMIREJO KEC. KEBUMEN Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kebumen', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'KEMITIR RT. 003 RW. 001 KEL. BUMIREJO KEC. KEBUMEN Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('07ce90a2-5141-4981-8964-201c86617b78', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'LUTFIL KHAKIM', '3305032702000002', 'KEBUMEN', '2000-02-27', 'L', 'Islam', 'belum_menikah', 'KEMILIRAN RT. 002 RW. 003 KEL. BUMIREJO KEC. PURING Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kebumen', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LUTFIL', 'Ibu LUTFIL', 'KEMILIRAN RT. 002 RW. 003 KEL. BUMIREJO KEC. PURING Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9bf142d7-bf66-4c92-874a-6d7bc38d317f', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'LILIK SAFRUDIN', '3308082208020005', 'MAGELANG', '2002-08-22', 'L', 'Islam', 'belum_menikah', 'DSN.DEMAKAN RT. 004 RW. 011 KEL. BANYUBIRU KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LILIK', 'Ibu LILIK', 'DSN.DEMAKAN RT. 004 RW. 011 KEL. BANYUBIRU KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3c3d96ca-34ad-40d8-a969-b1b4aa447c10', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'RIKHO RANGGA RAHMAYUDA', '3322081603040001', 'KAB. SEMARANG', '2004-03-16', 'L', 'Islam', 'belum_menikah', 'TEMPURAN RT. 001 RW. 003 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIKHO', 'Ibu RIKHO', 'TEMPURAN RT. 001 RW. 003 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f9e591a1-c986-40dd-a6b1-115ab3dab340', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'DENAND JAYATAMA', '3301022612030001', 'CILACAP', '2003-12-26', 'L', 'Islam', 'belum_menikah', 'JL. CANDRAYUDA RT. 001 RW. 006 KEL. PESANGGRAHAN KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENAND', 'Ibu DENAND', 'JL. CANDRAYUDA RT. 001 RW. 006 KEL. PESANGGRAHAN KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7e86c1b3-4bfe-4387-ae6c-5204b416a0f3', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ARKAAN NUR AERI', '3301210705030004', 'CILACAP', '2003-05-07', 'L', 'Islam', 'belum_menikah', 'JL. PENYU RT. 002 RW. 010 KEL. TEGALKAMULYAN KEC. CILACAP SELATAN Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ARKAAN', 'Ibu ARKAAN', 'JL. PENYU RT. 002 RW. 010 KEL. TEGALKAMULYAN KEC. CILACAP SELATAN Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b6f3082f-313a-4ee1-ae9a-82e548ef8149', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AHMAD ARIF MISBAKHUL MUSTAGHFIRIN', '3315161906040006', 'GROBOGAN', '2004-06-19', 'L', 'Islam', 'belum_menikah', 'LATAK RT. 004 RW. 003 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'LATAK RT. 004 RW. 003 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d3b13541-aa96-413a-b709-4b6743974889', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUCHAMAD ALI MIFTAH', '3319042602030002', 'KUDUS', '2003-02-26', 'L', 'Islam', 'belum_menikah', 'UNDAAN TENGAH GG 3 RT. 003 RW. 001 KEL. UNDAAN TENGAH KEC. UNDAAN Kab. Kudus JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUCHAMAD', 'Ibu MUCHAMAD', 'UNDAAN TENGAH GG 3 RT. 003 RW. 001 KEL. UNDAAN TENGAH KEC. UNDAAN Kab. Kudus JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2316a0a9-bc74-40bb-a084-292414639f5e', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ABDUL AZIS', '3322062409000004', 'KAB. SEMARANG', '2000-09-24', 'L', 'Islam', 'belum_menikah', 'CELENGAN RT. 004 RW. 002 KEL. LOPAIT KEC. TUNTANG Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ABDUL', 'Ibu ABDUL', 'CELENGAN RT. 004 RW. 002 KEL. LOPAIT KEC. TUNTANG Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e6bc496d-ce21-44b9-bbda-5c258c717beb', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'WIDI ASTUTI', '3328085007020002', 'TEGAL', '2002-07-10', 'P', 'Islam', 'belum_menikah', 'KARANGANYAR RT. 013 RW. 007 KEL. KARANGANYAR KEC. KEDUNGBANTENG KAB. TEGAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WIDI', 'Ibu WIDI', 'KARANGANYAR RT. 013 RW. 007 KEL. KARANGANYAR KEC. KEDUNGBANTENG KAB. TEGAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4964ae0c-ddd1-478f-b6e4-890a7ad6e923', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'WAHYU KURNIAWAN', '3315161602030002', 'GROBOGAN', '2003-02-16', 'L', 'Islam', 'belum_menikah', 'LATAK RT. 003 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WAHYU', 'Ibu WAHYU', 'LATAK RT. 003 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('51699438-9ba0-4d5d-bfeb-eb4fcf9e2e3e', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'AHMAD NUR RIZKY', '3322063107030001', 'KAB. SEMARANG', '2003-07-31', 'L', 'Islam', 'belum_menikah', 'BEJIREJO RT. 003 RW. 003 KEL. KALIBEJI KEC. TUNTANG Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'BEJIREJO RT. 003 RW. 003 KEL. KALIBEJI KEC. TUNTANG Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('66610291-af1a-42b5-8a53-16e64c2d19af', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'BILLY ENJANG ZANUARTA', '3322082901030001', 'SEMARANG', '2003-01-29', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 006 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BILLY', 'Ibu BILLY', 'KRAJAN RT. 006 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
-- Batch 2
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cd014d86-4309-417f-8816-71b659a7e549', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'IBNU SANTORO', '3301202109030003', 'CILACAP', '2003-09-21', 'L', 'Islam', 'belum_menikah', 'DUSUN REJASARI RT. 008 RW. 008 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah IBNU', 'Ibu IBNU', 'DUSUN REJASARI RT. 008 RW. 008 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('af6bd184-bfd3-4ff6-a555-f5152b2dfe63', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ALDI PRATAMA PUTRA', '1604241805030001', 'ULAK LEBAR', '2003-05-18', 'L', 'Islam', 'belum_menikah', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDI', 'Ibu ALDI', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6a764336-f944-4e66-ad6f-31397433be94', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'M. CHANDWA PUTRA', '3301201602050002', 'MAJALENGKA', '2006-02-16', 'L', 'Islam', 'belum_menikah', 'DUSUN RAWASARI RT. 010 RW. 007 KEL. RAWAJAYA KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'DUSUN RAWASARI RT. 010 RW. 007 KEL. RAWAJAYA KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f83ea8f2-2f1c-4cec-a409-217ca7f046be', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'AHMAD SUGONO', '3328051104930002', 'TEGAL', '1993-04-11', 'L', 'Islam', 'belum_menikah', 'KARANGANYAR RT. 004 RW. 002 KEL.KARANGANYAR KEC. PAGERBARANG Kab. Tegal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'KARANGANYAR RT. 004 RW. 002 KEL.KARANGANYAR KEC. PAGERBARANG Kab. Tegal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f97bdca1-8fcf-44a0-bacb-8c83d511c6e1', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AHMAD KHARIS', '3322070310020001', 'Kab. Semarang', '2002-10-03', 'L', 'Islam', 'belum_menikah', 'KARANG RT. 004 RW. 004 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083836187662', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'KARANG RT. 004 RW. 004 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', '083836187662', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('109792f0-a76d-4520-b6d5-5135e90f4335', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AKMAL TAUFIQUL AULIA', '3324072403020002', 'Kendal', '2002-03-24', 'L', 'Islam', 'belum_menikah', 'JL. SIMBANG NO. 6 RT. 001 RW. 006 KEL. BEBENGAN KEC. BOJA Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '‪081332049347', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AKMAL', 'Ibu AKMAL', 'JL. SIMBANG NO. 6 RT. 001 RW. 006 KEL. BEBENGAN KEC. BOJA Kab. Kendal JAWA TENGAH', '‪081332049347', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0ea12928-0c44-4c14-94ae-ee39a0a0c620', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'MUHAMAD AKHSINUL FUTUH', '3301022506030001', 'CILACAP', '2003-06-25', 'L', 'Islam', 'belum_menikah', 'JL. BUNTU NO. 05 RT. 003 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089672785870', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'JL. BUNTU NO. 05 RT. 003 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '089672785870', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('eedc555d-853f-4a84-acf8-57cf826c11e5', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'M. RIFQI ABRORI', '1802011706050003', 'KALIDADI', '2005-06-17', 'L', 'Islam', 'belum_menikah', 'DUSUN IV RT. 009 RW. 004 KEL. KALISARI KEC. KALIREJO KAB. LAMPUNG TENGAH LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085764607808', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'DUSUN IV RT. 009 RW. 004 KEL. KALISARI KEC. KALIREJO KAB. LAMPUNG TENGAH LAMPUNG', '085764607808', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c85ac4d4-3bdb-424c-bbea-4910c75f69db', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FAIQ MUHTAM', '3301022802010008', 'CILACAP', '2001-02-28', 'L', 'Islam', 'belum_menikah', 'JL. DIPAMENAWI NO. 32 RT. 001 RW. 014 KEL. KALISABUK KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089637722713', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAIQ', 'Ibu FAIQ', 'JL. DIPAMENAWI NO. 32 RT. 001 RW. 014 KEL. KALISABUK KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '089637722713', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e6ca51fb-6d3a-4283-9636-d0280d7a540c', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'MUHAMMAD HENDRAWAN ADITYA', '3324082110000003', 'KENDAL', '2000-10-21', 'L', 'Islam', 'belum_menikah', 'DK. RAGIL RT. 001 RW. 011 KEL. PLANTARAN KEC. KALIWUNGU SELATAN Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083108561114', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'DK. RAGIL RT. 001 RW. 011 KEL. PLANTARAN KEC. KALIWUNGU SELATAN Kab. Kendal JAWA TENGAH', '083108561114', 'approved', '2024-01-01', 'Data siswa dari CSV - Cancel');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9c2ed9c8-7bbf-4df9-bf77-fd7ff59100d7', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'AGHISNA ZAKI ABDUR ROHMAN', '3322150609050001', 'KAB. SEMARANG', '2005-09-06', 'L', 'Islam', 'belum_menikah', 'LINGKUNGAN KRAJAN KIDUL RT. 002 RW. 005 KEL. WUJIL KEC. BERGAS Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Semarang', 'Jawa Tengah', '12345', '082313662482', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AGHISNA', 'Ibu AGHISNA', 'LINGKUNGAN KRAJAN KIDUL RT. 002 RW. 005 KEL. WUJIL KEC. BERGAS Kab. Semarang JAWA TENGAH', '082313662482', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5542ee47-04ca-45fc-894f-43668f480d08', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'BAGUS PRASETYONO', '3322070304060001', 'KAB. SEMARANG', '2006-04-03', 'L', 'Islam', 'belum_menikah', 'DSN. JONGGRANGAN RT. 002 RW. 007 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Semarang', 'Jawa Tengah', '12345', '083865088759', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BAGUS', 'Ibu BAGUS', 'DSN. JONGGRANGAN RT. 002 RW. 007 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '083865088759', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('333056cd-4f46-4e72-b399-31c9247d8e07', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DWI SETYAWAN', '3275030712050011', 'BEKASI', '2005-12-07', 'L', 'Islam', 'belum_menikah', '" 	KP. PENGGILINGAN BARU RT. 002 RW. 003 KEL. HARAPANBARU KEC. BEKASI UTARA Kota Bekasi JAWA BARAT"', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083865088759', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', '" 	KP. PENGGILINGAN BARU RT. 002 RW. 003 KEL. HARAPANBARU KEC. BEKASI UTARA Kota Bekasi JAWA BARAT"', '083865088759', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f03379ae-7422-4703-9dec-4337dcc50ee4', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'REHAN SAIMAN PUTRA', '3275020206060011', 'JAKARTA', '2006-06-02', 'L', 'Islam', 'belum_menikah', 'BABAKAN CIANJUR RT. 001 RW. 031 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081241941930', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah REHAN', 'Ibu REHAN', 'BABAKAN CIANJUR RT. 001 RW. 031 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', '081241941930', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('36429773-26a3-44f1-a489-3d346f24cc8b', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'RIDHO ALFATH AZZAKI', '3216020905060012', 'JAKARTA', '2006-05-09', 'L', 'Islam', 'belum_menikah', 'KP. CIBUNGUR INDAH NO. 168 RT. 004 RW. 014 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '087821617619', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIDHO', 'Ibu RIDHO', 'KP. CIBUNGUR INDAH NO. 168 RT. 004 RW. 014 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR Kab. Karawang JAWA BARAT', '087821617619', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b7cba54b-1720-4a37-aa92-436147b09d67', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DYIMAST VEBRYANT SETTYAJY', '3307120802020002', 'WONOSOBO', '2002-02-08', 'L', 'Islam', 'belum_menikah', 'KALIJERUK RT. 003 RW. 004 KEL. SIWURAN KEC. GARUNG Kab. Wonosobo JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085700166137', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DYIMAST', 'Ibu DYIMAST', 'KALIJERUK RT. 003 RW. 004 KEL. SIWURAN KEC. GARUNG Kab. Wonosobo JAWA TENGAH', '085700166137', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7a8d3ef3-3df9-486a-a3d8-ff66451ffb63', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'M. ABDUL ROHMAN ASHARI', '1704071801980001', 'PASAR BARU', '1998-01-13', 'L', 'Islam', 'belum_menikah', 'PASAR BARU RT. 000 RW. 000 KEL. PASAR BARU KEC. NASAL Kab. Kaur BENGKULU', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081226214772', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'PASAR BARU RT. 000 RW. 000 KEL. PASAR BARU KEC. NASAL Kab. Kaur BENGKULU', '081226214772', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e7028cbc-49ed-488d-b181-85c905d21335', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AXSEL ADE PRADANA', '3322102812970001', 'KAB. SEMARANG', '1997-12-28', 'L', 'Islam', 'belum_menikah', 'KUPANG TEGAL RT. 004 RW. 004 KEL. KUPANG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085643674453', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AXSEL', 'Ibu AXSEL', 'KUPANG TEGAL RT. 004 RW. 004 KEL. KUPANG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '085643674453', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('52f26355-d747-4e2d-8b9b-bf19a7183d47', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ANANG KAROMAIN', '3322060509010008', 'KAB. SEMARANG', '2001-09-05', 'L', 'Islam', 'belum_menikah', 'CIKAL RT. 006 RW. 007 KEL. TUNTANG KEC. TUNTANG Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083138028476', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANANG', 'Ibu ANANG', 'CIKAL RT. 006 RW. 007 KEL. TUNTANG KEC. TUNTANG Kab. Semarang JAWA TENGAH', '083138028476', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1f966fab-f49f-4b1e-aa61-0540df10ffb7', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'RAMDHANI', '3305182610050002', 'KEBUMEN', '2005-10-26', 'L', 'Islam', 'belum_menikah', 'PEKUNCEN RT. 004 RW. 003 KEL. PEKUNCEN KEC. SEMPOR Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0882005544238', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAMDHANI', 'Ibu RAMDHANI', 'PEKUNCEN RT. 004 RW. 003 KEL. PEKUNCEN KEC. SEMPOR Kab. Kebumen JAWA TENGAH', '0882005544238', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f547297b-9555-4544-8b45-93766fb46de4', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MOHAMMAD LUBADUL FIKRI', '3301020203990006', 'CILACAP', '1999-03-02', 'L', 'Islam', 'belum_menikah', 'JL. KEBUN JERUK NO RT. 001 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0895385088904', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MOHAMMAD', 'Ibu MOHAMMAD', 'JL. KEBUN JERUK NO RT. 001 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', '0895385088904', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9e236bb6-6e39-41a9-8f16-a8d7fd299ba9', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'RAHMAD MORIES SYAHJIHAN', '3322092206960001', 'KAB. SEMARANG', '1996-06-22', 'L', 'Islam', 'belum_menikah', 'DSN. KENTENG RT. 001 RW. 007 KEL. SUMOWONO KEC. SUMOWONO Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085866560593', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAHMAD', 'Ibu RAHMAD', 'DSN. KENTENG RT. 001 RW. 007 KEL. SUMOWONO KEC. SUMOWONO Kab. Semarang JAWA TENGAH', '085866560593', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d39f539b-3f95-4cdf-9fa0-2fb18e106caa', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'ADE FITRIONO', '3303020512020002', 'PURBALINGGA', '2002-12-05', 'L', 'Islam', 'belum_menikah', 'KARANG NANGKA RT. 001 RW. 003 KEL. KARANGNANGKA KEC. BUKATEJA Kab. Purbalingga JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADE', 'Ibu ADE', 'KARANG NANGKA RT. 001 RW. 003 KEL. KARANGNANGKA KEC. BUKATEJA Kab. Purbalingga JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5297c3cc-55fc-499e-a685-d8e17c0288bc', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'SUROSO', '1801171503830010', 'PURWOREJO', '1983-03-15', 'L', 'Islam', 'belum_menikah', 'TAMAN SARI RT. 003 RW. 004 KEL. TRIMOMUKTI KEC. CANDIPURO Kab. Lampung Selatan LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SUROSO', 'Ibu SUROSO', 'TAMAN SARI RT. 003 RW. 004 KEL. TRIMOMUKTI KEC. CANDIPURO Kab. Lampung Selatan LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('91ebc809-7a4b-4cc3-8a36-2bbcdee0d070', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'CATUR SISWO UTOMO', '3315152108060001', 'GROBOGAN', '2006-08-21', 'L', 'Islam', 'belum_menikah', 'DSN TARUMAN RT. 001 RW. 002 KEL. TARUMAN KEC. KLAMBU Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '088238198301', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah CATUR', 'Ibu CATUR', 'DSN TARUMAN RT. 001 RW. 002 KEL. TARUMAN KEC. KLAMBU Kab. Grobogan JAWA TENGAH', '088238198301', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('049d3ed2-d66d-470a-94be-2820441dc2c1', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'DHIVA ANANDA LEO SANTOSO', '3321011508040007', 'DEMAK', '2004-08-15', 'L', 'Islam', 'belum_menikah', 'CANDISARI RT. 004 RW. 001 KEL. CANDISARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '088227280566', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DHIVA', 'Ibu DHIVA', 'CANDISARI RT. 004 RW. 001 KEL. CANDISARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', '088227280566', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4ca89a7e-9a35-4912-928e-cd357f5360fe', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'GALANG AJI PRATAMA', '3374163010000001', 'SEMARANG', '2000-10-30', 'L', 'Islam', 'belum_menikah', 'MANGUNHARJO DK PANGGUNG RT. 001 RW. 004 KEL. MANGUNHARJO KEC. TUGU KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089697270704', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GALANG', 'Ibu GALANG', 'MANGUNHARJO DK PANGGUNG RT. 001 RW. 004 KEL. MANGUNHARJO KEC. TUGU KOTA SEMARANG JAWA TENGAH', '089697270704', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0fe2b9cc-2e83-4a96-a3cb-5e52c8d7b789', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'SATRIYO AGUNG NUGROHO', '3503070804840001', 'TRENGGALEK', '1984-04-08', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN RT. 007 RW. 004 KEL. BOGORAN KEC. KAMPAK Kab. Trenggalek JAWA TIMUR', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SATRIYO', 'Ibu SATRIYO', 'DUSUN KRAJAN RT. 007 RW. 004 KEL. BOGORAN KEC. KAMPAK Kab. Trenggalek JAWA TIMUR', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('61b5c5b1-0876-4209-8d21-9c635ffccad5', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ILHAM ABDULLOH', '3301021006040001', 'CILACAP', '2004-06-10', 'L', 'Islam', 'belum_menikah', 'JL. SUKUN NO 06 RT. 001 RW. 006 KEL. KARANGKANDRI KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089504041907', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ILHAM', 'Ibu ILHAM', 'JL. SUKUN NO 06 RT. 001 RW. 006 KEL. KARANGKANDRI KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '089504041907', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('41dfa491-d247-48ac-a43d-70189bfbcb86', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'BAGAS SATRIO PRABOWO', '3301081603050003', 'CILACAP', '2005-03-16', 'L', 'Islam', 'belum_menikah', 'DUSUN CIPTOSARI RT. 003 RW. 003 KEL. BREBEG KEC. JERUKLEGI  KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083108636164', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BAGAS', 'Ibu BAGAS', 'DUSUN CIPTOSARI RT. 003 RW. 003 KEL. BREBEG KEC. JERUKLEGI  KAB. CILACAP JAWA TENGAH', '083108636164', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('39f77f23-48f1-4023-ba6a-2b0e0a346eac', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'DINA ELIVIA', '3374065302040003', 'SEMARANG', '2004-02-13', 'P', 'Islam', 'belum_menikah', 'PLAMONGANSARI RT. 004 RW. 012 KEL. PLAMONGANSARI KEC. PEDURUNGAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089504222183', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DINA', 'Ibu DINA', 'PLAMONGANSARI RT. 004 RW. 012 KEL. PLAMONGANSARI KEC. PEDURUNGAN KOTA SEMARANG JAWA TENGAH', '089504222183', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('46d4e57a-8f0d-49a8-9c75-a17956af3715', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'NOVITA PURNAMASARI', '3322084207030002', 'KAB. SEMARANG', '2002-07-02', 'P', 'Islam', 'belum_menikah', 'DSN. SETRO RT. 001 RW. 006 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083842329412', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NOVITA', 'Ibu NOVITA', 'DSN. SETRO RT. 001 RW. 006 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '083842329412', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8c294423-2f3f-4e6e-9d16-3218e8aaff6f', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'PRISTI HIKMAH WIJAYA', '3305104605030001', 'KEBUMEN', '2003-05-06', 'P', 'Islam', 'belum_menikah', 'DK KALIGAYAM RT. 002 RW. 001 KEL. KOROWELANG KEC. KUTOWINANGUN Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085870528040', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PRISTI', 'Ibu PRISTI', 'DK KALIGAYAM RT. 002 RW. 001 KEL. KOROWELANG KEC. KUTOWINANGUN Kab. Kebumen JAWA TENGAH', '085870528040', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ef3ed3e5-3f0e-4794-b28c-066c169b13c2', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'FAJAR APRIANTO', '3301201904030001', 'CILACAP', '2003-04-19', 'L', 'Islam', 'belum_menikah', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0895414903720', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAJAR', 'Ibu FAJAR', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '0895414903720', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4b4788b1-469a-411f-9a1a-1236c0e356d2', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'AYU KANIA PUTRI PRATAMA', '3315116907030001', 'GROBOGAN', '2003-07-29', 'P', 'Islam', 'belum_menikah', 'DUSUN TRISIK RT. 015 RW. 001 KEL. TARUB KEC. TAWANGHARJO KAAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0882006361342', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AYU', 'Ibu AYU', 'DUSUN TRISIK RT. 015 RW. 001 KEL. TARUB KEC. TAWANGHARJO KAAB. GROBOGAN JAWA TENGAH', '0882006361342', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b1d4a55f-0297-4c6b-9aa2-02d68658f05e', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'RUDIYANTO', '3329140709030001', 'BREBES', '2003-06-09', 'L', 'Islam', 'belum_menikah', 'Alamat akan diupdate', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0895414903720', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RUDIYANTO', 'Ibu RUDIYANTO', 'Alamat akan diupdate', '0895414903720', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a2022f2e-19b3-4845-9857-fe1e8d5f8457', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ORBAR SADEWO AHMAD', '3322122310050002', 'KAB. SEMARANG', '2005-10-23', 'L', 'Islam', 'belum_menikah', 'DSN. DOPLANG I RT. 003 RW. 004 KEL. PAKIS KEC. BRINGIN Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '088215749647', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ORBAR', 'Ibu ORBAR', 'DSN. DOPLANG I RT. 003 RW. 004 KEL. PAKIS KEC. BRINGIN Kab. Semarang JAWA TENGAH', '088215749647', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('47b08eab-0a27-44b3-81ae-a40741b472b8', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'ALIF ISMAYANA FADHILAH', '3305050504010001', 'KEBUMEN', '2001-04-05', 'L', 'Islam', 'belum_menikah', '" 	GEBANG RT. 003 RW. 002 KEL. JERUKAGUNG KEC. KLIRONG Kab. Kebumen JAWA TENGAH"', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '082322622118', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALIF', 'Ibu ALIF', '" 	GEBANG RT. 003 RW. 002 KEL. JERUKAGUNG KEC. KLIRONG Kab. Kebumen JAWA TENGAH"', '082322622118', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('45c17a25-b87c-4bfd-9319-9a4fc89c2265', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'LEO WISNU WIDODO', '3305262904010002', 'KEBUMEN', '2001-04-29', 'L', 'Islam', 'belum_menikah', 'WIDORO RT. 004 RW. 005 KEL. WIDORO KEC. KARANGSAMBUNG Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '087719764121', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LEO', 'Ibu LEO', 'WIDORO RT. 004 RW. 005 KEL. WIDORO KEC. KARANGSAMBUNG Kab. Kebumen JAWA TENGAH', '087719764121', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2c79fa78-8137-4959-9c0a-2e3b0ef10521', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'RITO SAEFUL ANWAR', '1903040704030001', 'CILACAP', '2003-04-07', 'L', 'Islam', 'belum_menikah', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081229170783', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RITO', 'Ibu RITO', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '081229170783', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('32d48815-51ad-43ca-be97-170688738895', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FERDY OKTAFIAN', '1807211910050002', 'LABUHAN RATU', '2005-10-19', 'L', 'Islam', 'belum_menikah', 'DUSUN BERINGIN RT. 004 RW. 001 KEL. LABUHAN RATU V KEC. LABUHAN RATU Kab. Lampung Timur LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FERDY', 'Ibu FERDY', 'DUSUN BERINGIN RT. 004 RW. 001 KEL. LABUHAN RATU V KEC. LABUHAN RATU Kab. Lampung Timur LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1238741c-fae7-4a07-80a4-27bf0e403c7b', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'RAFI MUHAMMAD MAJID', '3374082104990001', 'SEMARANG', '1999-04-21', 'L', 'Islam', 'belum_menikah', 'CANDI PERSIL NO.38 RT. 004 RW. 003 KEL. KALIWIRU KEC. CANDISARI Kota Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAFI', 'Ibu RAFI', 'CANDI PERSIL NO.38 RT. 004 RW. 003 KEL. KALIWIRU KEC. CANDISARI Kota Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a95e68bc-942e-453a-a1bd-fbc6484b57b7', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FAHED FERNANDA ADITYA', '3374081704010003', 'SEMARANG', '2001-04-17', 'L', 'Islam', 'belum_menikah', 'JANGLI KRAJAN RT. 010 RW. 006 KEL. KARANGANYAR GUNUNG KEC. CANDISARI Kota Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAHED', 'Ibu FAHED', 'JANGLI KRAJAN RT. 010 RW. 006 KEL. KARANGANYAR GUNUNG KEC. CANDISARI Kota Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2e20a4d1-4878-474d-b8b1-64e1a327e7f6', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'YOPI IMAYANA ANGGA SAPUTRA', '3322101112940002', 'KAB.SEMARANG', '1994-12-11', 'L', 'Islam', 'belum_menikah', 'JAGALAN RT. 003 RW. 007 KEL. KRANGGAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah YOPI', 'Ibu YOPI', 'JAGALAN RT. 003 RW. 007 KEL. KRANGGAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a6fa6d02-6c0f-4b31-a364-b87808d86cdb', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'ROCKY SETYAJI ADI SAPUTRA', '3301220509020002', 'CILACAP', '2002-09-05', 'L', 'Islam', 'belum_menikah', 'JL MASJID NO.17 C RT. 001 RW. 001 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ROCKY', 'Ibu ROCKY', 'JL MASJID NO.17 C RT. 001 RW. 001 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e4a9b12f-fb6b-4e3b-b99d-687f05b7e0ce', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'ACHMAD NANDANG PANGESTU', '3301223009990003', 'CILACAP', '1999-09-30', 'L', 'Islam', 'belum_menikah', 'JL RINJANI NO 208 RT. 005 RW. 016 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ACHMAD', 'Ibu ACHMAD', 'JL RINJANI NO 208 RT. 005 RW. 016 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('aafff434-b3db-482c-8bdc-05504124f78d', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AKHMAD KHOERURRIZKI', '3301011911060002', 'CILACAP', '2006-11-19', 'L', 'Islam', 'belum_menikah', 'DUSUN CIBABUT RT. 004 RW. 008 KEL. JATISARI KEC. KEDUNGREJA Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AKHMAD', 'Ibu AKHMAD', 'DUSUN CIBABUT RT. 004 RW. 008 KEL. JATISARI KEC. KEDUNGREJA Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ef0102e8-a908-4fee-ab71-b6544809b1c5', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'RISKI KURNIAWAN', '3322105712020003', 'KAB.SEMARANG', '2002-12-17', 'L', 'Islam', 'belum_menikah', 'DSN. KINTELAN RT. 013 RW. 005 KEL. PASEKAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RISKI', 'Ibu RISKI', 'DSN. KINTELAN RT. 013 RW. 005 KEL. PASEKAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0d203912-4949-40ae-8285-dd8815e3078e', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'M. NAUFAL FEBRIAN SUNANTO', '3324162102060001', 'KENDAL', '2006-02-21', 'L', 'Islam', 'belum_menikah', 'TAMBAKSARI RT. 003 RW. 005 KEL. TAMBAKSARI KEC. ROWOSARI Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'TAMBAKSARI RT. 003 RW. 005 KEL. TAMBAKSARI KEC. ROWOSARI Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('06d3b289-bd00-40c2-8eca-5c2873db5772', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'YOGA MAS PRASETYA', '3210152303030021', 'MAJALENGKA', '2002-03-22', 'L', 'Islam', 'belum_menikah', 'BLOK MANIS RT. 002 RW. 003 KEL. RANDEGAN KULON KEC. JATITUJUH Kab. Majalengka JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah YOGA', 'Ibu YOGA', 'BLOK MANIS RT. 002 RW. 003 KEL. RANDEGAN KULON KEC. JATITUJUH Kab. Majalengka JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c4fcad56-932e-48ef-9faf-a32f6e53aa2e', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MOHAMAD BAGUS ANJALU', '3505191511040003', 'BEKASI', '2004-11-15', 'L', 'Islam', 'belum_menikah', 'DSN TUWUHREJO RT. 004 RW. 001 KEL. KESAMBEN KEC. KESAMBEN KAB. BLITAR JAWA TIMUR', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MOHAMAD', 'Ibu MOHAMAD', 'DSN TUWUHREJO RT. 004 RW. 001 KEL. KESAMBEN KEC. KESAMBEN KAB. BLITAR JAWA TIMUR', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0e0a6ca1-c8f6-451c-9851-99a632fae7a8', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'SAMSUL MAARIF', '3327031112980003', 'PEMALANG', '1998-12-11', 'L', 'Islam', 'belum_menikah', 'MENDELEM RT. 003 RW. 008 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SAMSUL', 'Ibu SAMSUL', 'MENDELEM RT. 003 RW. 008 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d3ec41cd-d279-4b71-a4b2-2362543c1ba3', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'FATHAN MUBIN', '3327033006040003', 'PEMALANG', '2004-06-30', 'L', 'Islam', 'belum_menikah', 'DUKUH BULU RT. 005 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FATHAN', 'Ibu FATHAN', 'DUKUH BULU RT. 005 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5b4327e9-ba80-4da1-ad18-cf20643dc501', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'NUR JAMILUDIN', '3327030208010008', 'PEMALANG', '2001-08-02', 'L', 'Islam', 'belum_menikah', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK KAB. PEMALANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NUR', 'Ibu NUR', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK KAB. PEMALANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a24d5e61-bbda-4774-bb47-f0198d75bbd2', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'KHAUROHIM', '3301171412050001', 'CILACAP', '2005-12-14', 'L', 'Islam', 'belum_menikah', 'JLN SAWO NO 09 RT. 003 RW. 004 KEL. KARANGJATI KEC. SAMPANG Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHAUROHIM', 'Ibu KHAUROHIM', 'JLN SAWO NO 09 RT. 003 RW. 004 KEL. KARANGJATI KEC. SAMPANG Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('31dac155-de54-4065-861b-4bf36eb6884f', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'GASELA PUTRA ASRIL FAJWA TRYAWAN', '1604241508060002', 'ULAK LEBAR', '2006-08-15', 'L', 'Islam', 'belum_menikah', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GASELA', 'Ibu GASELA', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ec8581fd-73a5-4684-969f-c37cb5edb026', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUJIWAT AJI', '3322080805020002', 'KAB. SEMARANG', '2002-05-08', 'L', 'Islam', 'belum_menikah', 'JAMBU KULON RT. 003 RW. 003 KEL. JAMBU KEC. JAMBU Kab. Semarang JAWA TENGA', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUJIWAT', 'Ibu MUJIWAT', 'JAMBU KULON RT. 003 RW. 003 KEL. JAMBU KEC. JAMBU Kab. Semarang JAWA TENGA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c2574184-448e-47ad-8be6-107650db2701', 'b1b8fdb3-9d2e-48cd-8e63-4c7370bfeb6f', 'FAJAR AFTA FAHMATULLAILY', '3322182103010005', 'KAB. SEMARANG', '2001-03-21', 'L', 'Islam', 'belum_menikah', 'LANGENSARI BARAT RT. 007 RW. 006 KEL. LANGENSARI KEC. UNGARAN BARAT Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAJAR', 'Ibu FAJAR', 'LANGENSARI BARAT RT. 007 RW. 006 KEL. LANGENSARI KEC. UNGARAN BARAT Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6895c4bb-4489-4c71-ac9c-6b158f892e2a', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'JUVI DIAN PRATAMA', '3315162110040004', 'GROBOGAN', '2004-10-21', 'L', 'Islam', 'belum_menikah', 'DUSUN LATAK RT. 002 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah JUVI', 'Ibu JUVI', 'DUSUN LATAK RT. 002 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('885716f8-599d-4fee-b068-e8220b3164d7', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ADEVIA KHINANTI', '3325126604010002', 'BATANG', '2001-04-26', 'P', 'Islam', 'belum_menikah', 'SAWAHJOHO RT. 010 RW. 004 KEL. SAWAHJOHO KEC. WARUNGASEM Kab. Batang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADEVIA', 'Ibu ADEVIA', 'SAWAHJOHO RT. 010 RW. 004 KEL. SAWAHJOHO KEC. WARUNGASEM Kab. Batang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3f78f8de-875a-461b-8a09-bbc6493ae1cc', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ARLINDA', '3212315207990001', 'INDRAMAYU', '1999-07-12', 'P', 'Islam', 'belum_menikah', 'PASARBATANG RT. 002 RW. 011 KEL. PASARBATANG KEC. BREBES Kab. Brebes JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ARLINDA', 'Ibu ARLINDA', 'PASARBATANG RT. 002 RW. 011 KEL. PASARBATANG KEC. BREBES Kab. Brebes JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('62f49b3b-8917-43df-9e05-300721bad1b5', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'NAELI SIVA', '3305136105030001', 'KEBUMEN', '2003-05-21', 'P', 'Islam', 'belum_menikah', 'GONDANG RT. 002 RW. 008 KEL. KUWAYUHAN KEC. PEJAGOAN Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NAELI', 'Ibu NAELI', 'GONDANG RT. 002 RW. 008 KEL. KUWAYUHAN KEC. PEJAGOAN Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9b171db9-cf90-4e8d-8ebe-18e6f412b360', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'NANANG ZAENAL ARIFIN', '3322110204040004', 'KAB. SEMARANG', '2004-04-02', 'L', 'Islam', 'belum_menikah', 'DSN KLOWOH RT. 006 RW. 004 KEL. LEMAHIRENG KEC. BAWEN Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NANANG', 'Ibu NANANG', 'DSN KLOWOH RT. 006 RW. 004 KEL. LEMAHIRENG KEC. BAWEN Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('230601b3-dd5c-44bb-87b8-d979bc448852', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'KHOIRUL ANAM', '3322071501010001', 'KAB. SEMARANG', '2001-01-15', 'L', 'Islam', 'belum_menikah', 'KRAJAN II RT. 002 RW. 003 KEL. TEGARON KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHOIRUL', 'Ibu KHOIRUL', 'KRAJAN II RT. 002 RW. 003 KEL. TEGARON KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1a1ac9ae-8b4f-4189-b05b-c5b829230ad2', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'TRI WAHYU PAMUNGKAS', '3322102502960001', 'KAB. SEMARANG', '1996-02-25', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 005 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah TRI', 'Ibu TRI', 'KRAJAN RT. 005 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d3e795c1-3e1c-4eaf-9e4e-23e92d12c026', '000c963d-94bb-47e3-906b-9f21f7b5262d', 'KISNA NUR AWI', '3327030211030006', 'PEMALANG', '2003-11-02', 'L', 'Islam', 'belum_menikah', 'DK BULU RT. 004 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KISNA', 'Ibu KISNA', 'DK BULU RT. 004 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9ac70805-1a48-4c19-bf35-6f4ad171ac09', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'MUHAMMAD DAVA', '3172043112040004', 'JAKARTA', '2004-12-31', 'L', 'Islam', 'belum_menikah', 'DUSUN KOBAK KARIM RT. 012 RW. 004 KEL. KALANGSURYA KEC. RENGASDENGKLOK Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'DUSUN KOBAK KARIM RT. 012 RW. 004 KEL. KALANGSURYA KEC. RENGASDENGKLOK Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ebc52b0d-1962-41c2-9ac3-8effa0c13ae1', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'AFIF MAS UL', '3327030208010008', 'PEMALANG', '2001-08-02', 'L', 'Islam', 'belum_menikah', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AFIF', 'Ibu AFIF', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cf46ccf8-a6a3-4b34-980a-fef98da1e153', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'ALDA REFA', '3401121503990001', 'KULON PROGO', '1999-03-15', 'L', 'Islam', 'belum_menikah', 'KETILENG INDAH BLOK K-22.A RT. 002 RW. 012 KEL. SENDANGMULYO KEC. TEMBALANG Kota Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDA', 'Ibu ALDA', 'KETILENG INDAH BLOK K-22.A RT. 002 RW. 012 KEL. SENDANGMULYO KEC. TEMBALANG Kota Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3ff7ff4f-881f-40b8-ae30-36d00fb0d64d', '98d0deb4-4b90-4315-8361-d37abf6e88bf', 'RAVI ASHAR MAULANA', '3321010504000005', 'DEMAK', '2000-04-05', 'L', 'Islam', 'belum_menikah', 'JL. PUCANG SARI I NO. 26 RT. 001 RW. 017 KEL. BATURSARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAVI', 'Ibu RAVI', 'JL. PUCANG SARI I NO. 26 RT. 001 RW. 017 KEL. BATURSARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');

-- Insert Penempatan (110 records)
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c2d91fdd-7653-480e-a636-ac2e34ef99f1', 'fd804697-808a-48e4-aa6d-d6179ab8d02a', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1cd2a763-4e83-40d8-8102-eb0606734fe5', 'ded9b960-f721-4abe-8c28-8ace1d25edd4', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 181000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c3ff314a-c79d-420a-8d58-25094cd1ab7c', 'ce16aa9f-28c7-4864-a9d7-aa668f2d440f', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 182000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1e5505b9-e3eb-4049-9b85-6604415ca087', '166f7eea-d4c3-4271-978a-2fd6fa4dced6', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 183000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3d7328ba-90ef-4d8a-9ccf-bf5eda7c1b6a', '801a22f3-5948-4337-aa05-500f6b375f41', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 184000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('48c5dad3-42db-4e67-84bb-aceb37868695', '2091c33a-bd8c-42c8-926e-121010848182', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 185000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4e6a3ae9-fb10-462e-9259-7541b0f78418', 'f2cdac08-7503-4ee7-8b45-dec950827ef5', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 186000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('70ffff33-46ba-47f3-bff0-da6837a44ac9', '6a1c473e-bc58-43c3-b7af-0fab062e4eec', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 187000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2a811556-c4de-43b0-b090-ff42144aadda', '303e1f3d-de70-4028-bde5-dd837b86c254', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 188000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('88f7c50c-cf63-42b9-a8f0-08597efddc44', '55cdfe95-d094-499b-a6d1-e36e66e2c36c', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 189000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('385ec9a9-a3ff-4ed3-a8c7-61337e9f9cfe', '0240c4fa-fe58-462b-bffe-f5116e39c5a6', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 190000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5ae9e6d5-93d2-4b31-9e61-7e1e1363ad19', '1ec891d2-6825-4d9f-be43-f84cfd21a90f', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 191000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b00c5f57-b243-400a-b415-efe803d807db', 'be82a581-a4fb-4bbd-914a-199ab1991636', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 192000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3a4871e6-1299-4333-bcf7-4d95c7c1e2f6', 'f56067e8-0891-416d-945d-19ceb1856f78', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 193000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b28802f9-bae9-4e0e-a464-2f32d6757f0f', '59c919cd-9bfa-4ef5-b1b5-dbfcf58dc7ba', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 194000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b8ab4df3-1a32-4131-b91e-c961e242bff5', 'e5ea124d-5fb9-45e7-ade8-947b43fd2685', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 195000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('dea2bc36-e801-4dc9-b09f-51e023ea68fd', 'a4b14054-2194-466d-a1d4-fd50a5955cf7', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 196000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('631e995d-9cbe-462b-a397-5de9487d127d', '2bd2e84d-8c15-4a84-8f38-6ae4e9de8fea', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 197000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('39bae75f-33a4-423a-ae50-37f5f8bb2c68', '8de6e7e0-c06b-4491-a998-e53b287af938', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 198000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('78312c71-f535-444e-9397-a63f933ce709', '3fcc0772-c1e9-4f8f-85a6-d22cd362e31a', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 199000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f0189393-029b-41d7-ab31-928848a2f738', '5b520abe-3b8e-402a-b770-eb6febeb2c46', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 200000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2800806b-8b8b-4c48-8556-7d154ecd38b6', '0540a273-3e28-40a2-9e10-382766fd4df8', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 201000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fafd94f1-ed60-4f17-a906-cbdfdc055b41', '02923ade-6b0d-497e-a95a-15c12d54fd12', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 202000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('047e1f3d-36e1-40bb-a48e-03bd92a8b7d5', 'fdd82101-956e-4e0c-a5d4-e06d54b35a43', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 203000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('11abe11e-988b-47df-8d19-dbe4e1a92ce2', 'b0f48966-c057-4701-a274-e0ceb9bbb714', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 204000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('92b6950f-cb3e-4614-9a48-2e77ef7e347c', 'faeda35d-910b-42e8-9c39-f34ddf8c6c2b', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 205000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e72b23e3-6592-4c7c-b175-7289d6812c1b', 'dd206734-a298-4285-861c-80cf4ba0b107', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 206000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('988d3620-da47-46aa-a116-a8d8ba3ebdd6', '4f3633e6-c4fa-461c-80ac-e6771fd7ed4e', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 207000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1567878f-6071-4568-ae19-a6c42c67eecf', '854b9504-4ed4-492b-8b51-5b888b3f26d9', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 208000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3b2441dd-b081-4a4c-9f6d-61afc2341315', '60f76f7d-94da-469d-a1ad-6cbbb0ecc3e6', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 209000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1b2dc367-109a-4fae-9b2a-6a65870ff827', '5d01d407-5fa0-4424-8ab0-8116b36b1f5e', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 210000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e69560fb-b34f-41c8-99d0-1e807fa7fb3f', 'c6900713-70a9-4365-a566-05fc5ef0568d', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 211000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fb0287a6-90a9-4f14-86e1-4f536e465820', '454553b1-ef92-48fd-abc1-9f9ea115c57c', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 212000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e66a9a0d-ad6f-40bb-8e62-e008ae219670', '2347d962-ceb0-42f1-ae8b-598adab24cda', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 213000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('0d687f2b-0fa2-4ea3-85de-d0d80c2224a0', '3b8b368d-cdc4-4e57-9372-06a305ab74e2', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 214000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1288ae96-6eba-46fe-8803-29b6dba96a51', '82e5afdc-edba-45fc-bdfa-8e615ba798dc', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 215000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b0acba3a-bdf6-404f-a7ff-9cf6fcad1b04', '45acd3db-e5bf-4cea-a619-497371708c56', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 216000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d1d0b3dd-6417-4d97-ac0f-7005c41a447a', '190dc968-920c-448a-98a4-467ec594f2a8', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 217000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e250ca38-5626-4e6b-aeb8-65662511df8e', '2532449b-eb67-45b0-b694-1366f05d89ae', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 218000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2acb420b-8a34-4362-9bc4-ba8514d218cb', '8292b88f-1f18-47a8-91bd-cd417eb780ee', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 219000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('580ab0e0-88a2-4b01-9d7b-2000a3773f79', 'c8553f50-717e-4d04-8660-c935d2896c15', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 220000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('511e1924-c2d4-4dbf-a67f-e51109fbcb35', '64a1f604-fc02-4097-87c3-722c4ce8dd1e', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 221000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5d87d1ce-8c34-431b-a5eb-8640b4d9b29d', '4c7614ed-1f3a-423e-8b28-be9f1942af1e', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 222000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('0d8a0767-6398-4faf-a0e6-f019c96b2066', '7b1b3190-6d15-4e66-bea3-eb6e0e7a13f5', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 223000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8a2d9b06-d40a-4e80-9533-791ae7aab83d', '2ed2c1b0-3e58-49bc-9e4f-5655a56a75f4', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 224000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d7b36d44-d344-4ea2-8e89-5fb3fb88ecfb', 'c06e5f94-1e52-4f69-b49f-149c17d230ae', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 225000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d9ce3db9-fe02-4fb1-ab1b-1e3b256ecdde', 'cf3a80c0-9e43-4dab-9576-939fd231ce04', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 226000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8295a3a2-a37e-4c93-afce-144f5bbf8457', '58dcb08f-e97c-40ae-9105-ce5b6c76e3b3', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 227000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('525ed114-da6e-4a54-bf78-356329dc6503', '8645918b-2aae-46b2-899b-149967ca3cdc', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 228000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8d6c9133-edc6-482f-a727-bfa360a16590', '3c70a728-ddd2-4d2d-be4c-28bbc34464d0', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 229000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('06a4ec36-6eea-4764-bb62-e553b6db7066', 'e75c44c6-3756-4b58-ad2c-05181f409b78', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 230000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b8678932-84b4-4b19-aa2e-d454432628c1', 'd5e480bf-7dfc-4d82-9d98-eda3f09ba385', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 231000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('9076de4a-5e9a-4f84-b7b5-23947e35c486', '84f9e7c0-4ad4-4d31-a78b-467c2488ee2b', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 232000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('372cb116-d562-4d38-b2e6-fe95ac7f6e6e', '817853d8-fafc-4b3e-b148-b01ffa960cf4', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 233000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('15941d35-5b4c-4ff8-87a4-902d2e2a6d90', '12383f62-2ba7-4e6c-bf15-598c8eb010d3', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 234000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('30e4b3e8-8a76-43c0-ba62-b929bbf801bf', '200f4845-a595-4370-bd2f-1c820edfa9dc', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 235000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('98406871-18b9-4375-9958-98e134095b15', 'a3229a08-f2ee-48d5-8d21-26d2332e3264', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 236000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c5765364-ad2a-47c3-9167-9e3edb8c6b36', '17cc4d60-db1c-4529-a55c-7aacad7d4e73', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 237000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3d85e5fb-3ec8-48b8-aeff-bfb7a88d0317', '26d22384-2363-4946-afe8-2e2b2aeb8bc8', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 238000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a6ae6f44-9142-4191-b1da-22f4ad44868c', 'f32df7b3-8de6-4a59-8b08-10f56e3ea3ee', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 239000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('67444dad-8bc0-4798-896d-b62ab761afa0', '659f76ee-a55d-414e-ad97-28fa3fa9d9c9', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 240000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e9cea5a5-7e72-46b3-a953-5a8b3a1e72c7', 'cc795350-ea84-4883-817e-335daa71d3f1', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 241000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1f463f5d-2532-4be2-a48f-6f0004dbcd43', '6b7d0293-4bce-4b1a-b166-996b79c0e66b', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 242000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fcf44a5e-3060-473f-af7f-8626d5f5a8c7', '1d8cd2d7-cc97-4eba-a1d6-f6ab7e1f62b2', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 243000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2a4ebe08-09cd-44cc-986e-c792111be11f', '34bc6573-3232-4821-9b5b-4e7aa128497b', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 244000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('cfcf34a6-1a1b-4e4e-b796-0b680738d0c3', 'd4ff49e2-4a2b-4b99-94be-bb30577de1f4', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 245000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('cf51100c-5309-487b-88c5-e956a72ef046', 'b16caea0-5994-459d-9b2b-d27ddd26725f', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 246000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('17e64375-6756-49cd-92b9-8303f10beb3e', '0802e511-143f-4ef6-a3ea-15afd037902f', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 247000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('0b1d533e-533a-45a1-8de2-3bf723a76cec', '547f7032-11f0-4a7b-b8fe-2c27f387d085', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 248000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('97bff6a3-ee16-4f3a-b81c-129b945ea584', '5df87fca-5b81-493b-9812-335cd6721881', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 249000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f72f3b38-131d-456f-9bd7-594c62fb260f', '043c16b8-f58c-44a8-a5d5-d2d240159bc6', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 250000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d3493e32-3af6-4b0e-964f-2518912f5e4d', 'c45cd002-ae88-4f9b-8d1e-67a43360845c', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 251000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c726045c-6800-48a9-a9a6-15eb64127107', '30f5011d-1604-4aea-b1a1-e4b677c5a319', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 252000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f6efe1a8-e01a-498a-a0ba-37a97d26b071', '4e7a11a3-f520-4a87-8eda-5ee4762ed08b', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 253000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('df9c8e4b-e2cc-419d-b603-246301847dfa', 'ec48a490-1c59-454e-baee-d7be4e820be0', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 254000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f33003f1-602e-473c-8c53-f4dd163394c5', 'ad887934-ed0e-4b28-b2d4-69cbd75ca052', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 255000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('bfde4694-90db-401f-8a09-f78d565f6c45', '86b7eb9b-87e7-45dd-b264-066b45588b78', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 256000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fa73728c-cf61-4f0b-8e8d-a330f81aad49', '87ee7ab4-ba21-4fb1-8255-1e3ff3ca8c85', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 257000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('46d157bb-08b1-4b90-a3b8-6847117b40d3', 'e6e4b853-9ece-47df-a0d0-d2c3256afda1', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 258000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('04e7d84e-fb4b-474e-bfc9-6d7f99322edf', '44b5123d-0340-45cb-bda8-ae1c3c4a4c20', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 259000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d8e7763c-645b-4109-b8b7-14ee6ac14cbd', '6e026dbc-710b-49ec-a76a-c7227433441a', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 260000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c9108094-38e4-4736-a900-0901fcfb1534', '1c83a931-b854-4d31-afee-2a5853999be1', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 261000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d32ed3e8-1830-4ec4-afb4-512a9aa8fedc', '61d6e45f-72fd-42b2-902d-baa739611b12', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 262000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('896ba788-363b-46c7-8f10-a5cf275193ae', 'e6a2e7cf-82f1-4e38-b266-63c97e53027a', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 263000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('867e20fd-dc10-4b75-b540-9788c8dbd91c', 'd79365f5-1359-4215-adac-3dd7e9848f01', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 264000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('596c8d6b-49e7-4b30-b7d2-716c1cf93782', '225c264d-7d6b-44ec-b6d4-9d192e9324f9', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 265000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('9765ac7e-23db-446d-a75e-d10e74a2e48b', 'b5b3ab28-1260-4e70-b594-29df3208c903', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 266000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d530ee63-6e07-477b-9d4d-e3fc2a52404a', '03e9dc01-a80f-4edf-9878-179da9b79dba', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 267000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8e2d5b40-6e1a-44c8-888d-ed6f4cdce111', '07ce90a2-5141-4981-8964-201c86617b78', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 268000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('766b26a1-a454-476a-a048-d6849a965acd', '9bf142d7-bf66-4c92-874a-6d7bc38d317f', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 269000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f03aff95-dfc2-4e08-8f6e-98fdac8ed0b8', '3c3d96ca-34ad-40d8-a969-b1b4aa447c10', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 270000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5c4f6fa2-39fa-4132-b64d-44c65076e52c', 'f9e591a1-c986-40dd-a6b1-115ab3dab340', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 271000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e1c7cc13-52f0-4814-9ad3-4f74e87474d6', '7e86c1b3-4bfe-4387-ae6c-5204b416a0f3', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 272000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('67b17ae6-75d6-45b5-a536-6108cdb314b4', 'b6f3082f-313a-4ee1-ae9a-82e548ef8149', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 273000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c6f99579-9420-4794-86c1-5ceb60a1136e', 'd3b13541-aa96-413a-b709-4b6743974889', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 274000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('10beebe2-eb64-4a4a-8383-7762324ca209', '2316a0a9-bc74-40bb-a084-292414639f5e', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 275000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e6dc42b5-09a0-42c8-8501-295baecef8c4', '4964ae0c-ddd1-478f-b6e4-890a7ad6e923', 'dca010e2-4876-4c98-8055-f55da1081704', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 277000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5e23458e-ecf7-4693-a663-ce7ebdafc992', '51699438-9ba0-4d5d-bfeb-eb4fcf9e2e3e', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 278000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('82ac431d-ff11-41c6-83a4-0dfae5474b1f', '66610291-af1a-42b5-8a53-16e64c2d19af', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 279000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3c49583a-208f-42c1-a653-34d27e2ed9fe', 'cd014d86-4309-417f-8816-71b659a7e549', 'ab857730-eaba-4817-9eda-85cc96db295c', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 280000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a09853ee-74cc-4a78-9364-90de1b16d0ff', 'af6bd184-bfd3-4ff6-a555-f5152b2dfe63', 'dca010e2-4876-4c98-8055-f55da1081704', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 281000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1005a7f0-5aaf-4240-8ffc-397073a9c2f7', '6a764336-f944-4e66-ad6f-31397433be94', '194dd5bd-3e35-41ef-8306-9b777e58475a', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 282000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('36326296-23d6-4d5c-930f-50a2519b638f', 'f83ea8f2-2f1c-4cec-a409-217ca7f046be', 'b023f6ae-066c-4800-8335-cf806ef6de21', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 283000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3ac64c9f-e536-4a49-9e3a-c6e5157ba5c3', 'f97bdca1-8fcf-44a0-bacb-8c83d511c6e1', 'ab857730-eaba-4817-9eda-85cc96db295c', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 284000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('29063660-fa41-4117-86e3-0842b0c7c79d', '109792f0-a76d-4520-b6d5-5135e90f4335', 'dca010e2-4876-4c98-8055-f55da1081704', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 285000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('599071f6-0507-4381-a1fe-58e2c77d7b70', '0ea12928-0c44-4c14-94ae-ee39a0a0c620', '194dd5bd-3e35-41ef-8306-9b777e58475a', 'c54acdd5-0363-4812-a2de-89875f053111', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 286000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('92365034-2ee0-424a-9f52-89abc161fc70', 'eedc555d-853f-4a84-acf8-57cf826c11e5', 'b023f6ae-066c-4800-8335-cf806ef6de21', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 287000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f5b81b3c-ad8a-4d32-a2c9-22b4b3b21e13', 'c85ac4d4-3bdb-424c-bbea-4910c75f69db', 'ab857730-eaba-4817-9eda-85cc96db295c', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 288000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('197adee1-e957-496f-80bb-68b4aaff1635', '9c2ed9c8-7bbf-4df9-bf77-fd7ff59100d7', '194dd5bd-3e35-41ef-8306-9b777e58475a', '9a1cb569-adfb-4ae2-9b2d-ef0e5077ee18', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 290000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5e1b9128-f047-48e4-a3b8-3a3fbfb7205c', '5542ee47-04ca-45fc-894f-43668f480d08', 'b023f6ae-066c-4800-8335-cf806ef6de21', '993a5bea-db58-4b06-83b7-4b93dd10ce8f', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 291000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- ✅ FULL seed data import completed successfully!
-- 🎉 Dashboard Magang Jepang ready with 170 students!

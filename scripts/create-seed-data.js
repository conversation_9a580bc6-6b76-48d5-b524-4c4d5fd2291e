/**
 * Script untuk membuat seed data lengkap dari <PERSON>V
 * Dashboard Sistem Magang Jepang - Yutaka LPK
 */

const fs = require('fs');
const path = require('path');

// Generate UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Clean data function
function clean(value) {
    if (!value || value === '' || value === '#REF!' || value === '#N/A') {
        return null;
    }
    return value.toString().trim();
}

// Convert Excel date to ISO
function excelToDate(excelDate) {
    if (!excelDate || isNaN(excelDate)) return '1995-01-01';
    const date = new Date((excelDate - 25569) * 86400 * 1000);
    return date.toISOString().split('T')[0];
}

async function createSeedData() {
    console.log('🚀 Creating seed data for Dashboard Magang Jepang...');
    
    // Read CSV
    const csvPath = path.join(__dirname, '..', 'seed data awal 2025.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n').filter(line => line.trim());
    
    console.log(`📊 Processing ${lines.length} lines...`);
    
    // Create default LPK data
    const yutakaId = generateUUID();
    const lpkDummyId = generateUUID();
    
    const lpkData = [
        {
            id: yutakaId,
            nama_lpk: 'Yutaka',
            alamat_lengkap: 'Jl. Raya Utama No. 123, Jakarta Pusat 10110',
            kota: 'Jakarta',
            provinsi: 'DKI Jakarta',
            nama_pimpinan: 'Bapak Yutaka Tanaka',
            kontak_person: 'Ibu Sari Wijaya',
            nomor_telepon: '021-1234-5678',
            email: '<EMAIL>',
            website: 'https://yutaka.co.id',
            status: 'aktif',
            tanggal_kerjasama: '2024-01-01',
            catatan: 'LPK Yutaka - Partner utama untuk program magang Jepang'
        },
        {
            id: lpkDummyId,
            nama_lpk: 'LPK Dummy',
            alamat_lengkap: 'Jl. Pendidikan No. 456, Bandung 40123',
            kota: 'Bandung',
            provinsi: 'Jawa Barat',
            nama_pimpinan: 'Bapak Pimpinan LPK',
            kontak_person: 'Ibu Kontak Person',
            nomor_telepon: '022-9876-5432',
            email: '<EMAIL>',
            website: 'https://lpkdummy.co.id',
            status: 'aktif',
            tanggal_kerjasama: '2024-01-01',
            catatan: 'LPK Dummy untuk testing dan development'
        }
    ];
    
    // Create Kumiai data
    const kumiaiData = [
        {
            id: generateUUID(),
            nama_kumiai: 'Gokei Cloud Kyodo Kumiai',
            kode_kumiai: 'GOKEI',
            alamat_jepang: '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002',
            kota_jepang: 'Tokyo',
            prefektur: 'Tokyo',
            kontak_person: 'Tanaka San',
            nomor_telepon: '+81-3-1234-5678',
            email: '<EMAIL>',
            website: 'https://gokei.jp',
            status: 'aktif',
            keterangan: 'Kumiai utama untuk program magang'
        },
        {
            id: generateUUID(),
            nama_kumiai: 'TIC Kyodo Kumiai',
            kode_kumiai: 'TIC',
            alamat_jepang: '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022',
            kota_jepang: 'Tokyo',
            prefektur: 'Tokyo',
            kontak_person: 'Yamada San',
            nomor_telepon: '+81-3-2345-6789',
            email: '<EMAIL>',
            website: 'https://tic.jp',
            status: 'aktif',
            keterangan: 'Kumiai untuk bidang teknik'
        }
    ];
    
    // Create sample Perusahaan data
    const perusahaanData = [
        {
            id: generateUUID(),
            kumiai_id: kumiaiData[0].id,
            nama_perusahaan: 'Tokyo Manufacturing Co., Ltd.',
            alamat_jepang: '3-3-3 Minato, Minato-ku, Tokyo 105-0003',
            kota_jepang: 'Tokyo',
            prefektur: 'Tokyo',
            bidang_usaha: 'Manufacturing',
            kontak_person: 'Sato San',
            nomor_telepon: '+81-3-3456-7890',
            email: '<EMAIL>',
            website: 'https://tokyo-mfg.jp',
            status: 'aktif',
            keterangan: 'Perusahaan manufaktur terkemuka di Tokyo'
        },
        {
            id: generateUUID(),
            kumiai_id: kumiaiData[1].id,
            nama_perusahaan: 'Osaka Technical Industries',
            alamat_jepang: '4-4-4 Namba, Chuo-ku, Osaka 542-0076',
            kota_jepang: 'Osaka',
            prefektur: 'Osaka',
            bidang_usaha: 'Technical Services',
            kontak_person: 'Suzuki San',
            nomor_telepon: '+81-6-4567-8901',
            email: '<EMAIL>',
            website: 'https://osaka-tech.jp',
            status: 'aktif',
            keterangan: 'Perusahaan teknik di Osaka'
        }
    ];
    
    // Process siswa data from CSV
    const siswaData = [];
    const penempatanData = [];
    
    let processedCount = 0;
    
    for (let i = 1; i < lines.length && i <= 50; i++) { // Limit to first 50 for testing
        const columns = lines[i].split(';');
        if (columns.length < 5) continue;
        
        const nama = clean(columns[1]);
        const nik = clean(columns[2]);
        
        if (!nama || !nik) continue;
        
        const siswaId = generateUUID();
        const gender = clean(columns[8]);
        const tempatLahir = clean(columns[10]) || 'Jakarta';
        const tglLahir = clean(columns[11]);
        const email = clean(columns[12]);
        const alamat = clean(columns[13]) || 'Alamat akan diupdate';
        const noHp = clean(columns[15]) || '081234567890';
        const kotaDaerah = clean(columns[42]) || 'Jakarta';
        
        // Assign to Yutaka by default, LPK Dummy for some variety
        const lpkId = (i % 4 === 0) ? lpkDummyId : yutakaId;
        
        const siswa = {
            id: siswaId,
            lpk_id: lpkId,
            nama_lengkap: nama,
            nik: nik,
            tempat_lahir: tempatLahir,
            tanggal_lahir: excelToDate(tglLahir),
            jenis_kelamin: (gender && gender.includes('Perempuan')) ? 'P' : 'L',
            agama: 'Islam',
            status_pernikahan: 'belum_menikah',
            alamat_lengkap: alamat,
            kelurahan: 'Kelurahan Sample',
            kecamatan: 'Kecamatan Sample',
            kota_kabupaten: kotaDaerah,
            provinsi: 'Jawa Tengah',
            kode_pos: '12345',
            nomor_hp: noHp,
            email: email,
            pendidikan_terakhir: 'SMA',
            nama_sekolah: 'SMA Negeri 1',
            tahun_lulus: 2020,
            jurusan: 'IPA',
            nama_ayah: 'Ayah ' + nama.split(' ')[0],
            nama_ibu: 'Ibu ' + nama.split(' ')[0],
            alamat_keluarga: alamat,
            nomor_hp_keluarga: noHp,
            status_pendaftaran: 'approved',
            tanggal_daftar: '2024-01-01',
            catatan: `Data siswa dari CSV - baris ${i}`
        };
        
        siswaData.push(siswa);
        
        // Create placement for some students
        if (i % 3 === 0) { // Every 3rd student gets placement
            const penempatan = {
                id: generateUUID(),
                siswa_id: siswaId,
                perusahaan_id: perusahaanData[i % 2].id,
                kumiai_id: kumiaiData[i % 2].id,
                tanggal_penempatan: '2024-01-15',
                tanggal_keberangkatan: '2024-06-01',
                tanggal_kepulangan: null,
                status_penempatan: 'aktif',
                posisi_kerja: 'General Worker',
                gaji_aktual: 180000,
                alamat_kerja: perusahaanData[i % 2].alamat_jepang,
                evaluasi_bulanan: null,
                catatan_khusus: 'Penempatan melalui program magang reguler'
            };
            
            penempatanData.push(penempatan);
        }
        
        processedCount++;
    }
    
    console.log(`✅ Processed ${processedCount} students`);
    console.log(`📊 Summary:`);
    console.log(`   - LPK: ${lpkData.length}`);
    console.log(`   - Kumiai: ${kumiaiData.length}`);
    console.log(`   - Perusahaan: ${perusahaanData.length}`);
    console.log(`   - Siswa: ${siswaData.length}`);
    console.log(`   - Penempatan: ${penempatanData.length}`);
    
    // Generate SQL
    const sql = generateSQL({
        lpk: lpkData,
        kumiai: kumiaiData,
        perusahaan: perusahaanData,
        siswa: siswaData,
        penempatan: penempatanData
    });
    
    // Save files
    const sqlPath = path.join(__dirname, 'seed-data-yutaka.sql');
    const jsonPath = path.join(__dirname, 'seed-data-yutaka.json');
    
    fs.writeFileSync(sqlPath, sql);
    fs.writeFileSync(jsonPath, JSON.stringify({
        lpk: lpkData,
        kumiai: kumiaiData,
        perusahaan: perusahaanData,
        siswa: siswaData,
        penempatan: penempatanData
    }, null, 2));
    
    console.log(`✅ Files generated:`);
    console.log(`   - SQL: ${sqlPath}`);
    console.log(`   - JSON: ${jsonPath}`);
}

function generateSQL(data) {
    let sql = `-- Seed Data Dashboard Magang Jepang - Yutaka LPK
-- Generated: ${new Date().toISOString()}
-- Records: LPK(${data.lpk.length}), Kumiai(${data.kumiai.length}), Perusahaan(${data.perusahaan.length}), Siswa(${data.siswa.length}), Penempatan(${data.penempatan.length})

-- Disable foreign key checks
SET session_replication_role = replica;

`;

    // LPK Mitra
    sql += `-- Insert LPK Mitra\n`;
    data.lpk.forEach(item => {
        sql += `INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('${item.id}', '${item.nama_lpk}', '${item.alamat_lengkap}', '${item.kota}', '${item.provinsi}', '${item.nama_pimpinan}', '${item.kontak_person}', '${item.nomor_telepon}', '${item.email}', '${item.website}', '${item.status}', '${item.tanggal_kerjasama}', '${item.catatan}');\n`;
    });
    
    // Kumiai
    sql += `\n-- Insert Kumiai\n`;
    data.kumiai.forEach(item => {
        sql += `INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('${item.id}', '${item.nama_kumiai}', '${item.kode_kumiai}', '${item.alamat_jepang}', '${item.kota_jepang}', '${item.prefektur}', '${item.kontak_person}', '${item.nomor_telepon}', '${item.email}', '${item.website}', '${item.status}', '${item.keterangan}');\n`;
    });
    
    // Perusahaan
    sql += `\n-- Insert Perusahaan\n`;
    data.perusahaan.forEach(item => {
        sql += `INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('${item.id}', '${item.kumiai_id}', '${item.nama_perusahaan}', '${item.alamat_jepang}', '${item.kota_jepang}', '${item.prefektur}', '${item.bidang_usaha}', '${item.kontak_person}', '${item.nomor_telepon}', '${item.email}', '${item.website}', '${item.status}', '${item.keterangan}');\n`;
    });
    
    // Siswa
    sql += `\n-- Insert Siswa\n`;
    data.siswa.forEach(item => {
        sql += `INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('${item.id}', '${item.lpk_id}', '${item.nama_lengkap.replace(/'/g, "''")}', '${item.nik}', '${item.tempat_lahir}', '${item.tanggal_lahir}', '${item.jenis_kelamin}', '${item.agama}', '${item.status_pernikahan}', '${item.alamat_lengkap.replace(/'/g, "''")}', '${item.kelurahan}', '${item.kecamatan}', '${item.kota_kabupaten}', '${item.provinsi}', '${item.kode_pos}', '${item.nomor_hp}', ${item.email ? `'${item.email}'` : 'NULL'}, '${item.pendidikan_terakhir}', '${item.nama_sekolah}', ${item.tahun_lulus}, '${item.jurusan}', '${item.nama_ayah}', '${item.nama_ibu}', '${item.alamat_keluarga.replace(/'/g, "''")}', '${item.nomor_hp_keluarga}', '${item.status_pendaftaran}', '${item.tanggal_daftar}', '${item.catatan.replace(/'/g, "''")}');\n`;
    });
    
    // Penempatan
    sql += `\n-- Insert Penempatan\n`;
    data.penempatan.forEach(item => {
        sql += `INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('${item.id}', '${item.siswa_id}', '${item.perusahaan_id}', '${item.kumiai_id}', '${item.tanggal_penempatan}', '${item.tanggal_keberangkatan}', ${item.tanggal_kepulangan ? `'${item.tanggal_kepulangan}'` : 'NULL'}, '${item.status_penempatan}', '${item.posisi_kerja}', ${item.gaji_aktual}, '${item.alamat_kerja}', ${item.evaluasi_bulanan ? `'${item.evaluasi_bulanan}'` : 'NULL'}, '${item.catatan_khusus}');\n`;
    });
    
    sql += `\n-- Re-enable foreign key checks\nSET session_replication_role = DEFAULT;\n\n-- ✅ Seed data import completed!\n`;
    
    return sql;
}

// Run script
if (require.main === module) {
    createSeedData();
}

module.exports = { createSeedData };

-- =====================================================
-- Seed Data Migration for Dashboard Magang Jepang
-- Supabase PostgreSQL Version
-- =====================================================

-- This migration inserts initial seed data
-- Run this after the initial schema migration

-- Insert initial Jenis Dokumen (Document Types)
INSERT INTO jenis_dokumen (id, nama_dokumen, deskripsi, wajib, kategori, urutan_tampil) VALUES 
(uuid_generate_v4(), 'KTP', 'Kartu Tanda Penduduk', true, 'personal', 1),
(uuid_generate_v4(), 'Kartu Keluarga', 'Kartu Keluarga', true, 'personal', 2),
(uuid_generate_v4(), '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', true, 'personal', 3),
(uuid_generate_v4(), 'Ija<PERSON><PERSON>', 'Ijazah pendidikan terakhir', true, 'pendidikan', 4),
(uuid_generate_v4(), 'Transkrip Nilai', 'Transkrip nilai pendidikan terakhir', true, 'pendidikan', 5),
(uuid_generate_v4(), 'Sertifikat Kesehatan', 'Sertifikat kesehatan dari dokter', true, 'kesehatan', 6),
(uuid_generate_v4(), 'Surat Keterangan Catatan Kepolisian', 'SKCK dari Polres setempat', true, 'legal', 7),
(uuid_generate_v4(), 'Paspor', 'Paspor yang masih berlaku', true, 'legal', 8),
(uuid_generate_v4(), 'Foto 4x6', 'Foto berwarna ukuran 4x6 cm', true, 'personal', 9),
(uuid_generate_v4(), 'Surat Pernyataan Orang Tua', 'Surat pernyataan persetujuan orang tua', true, 'legal', 10);

-- Insert initial Program Pendidikan (Training Programs)
INSERT INTO program_pendidikan (id, nama_program, deskripsi, durasi_bulan, biaya, kurikulum, sertifikat, status) VALUES 
(uuid_generate_v4(), 'Bahasa Jepang Dasar', 'Program pembelajaran bahasa Jepang tingkat dasar untuk persiapan magang', 3, 2500000, 'Hiragana, Katakana, Kanji dasar, Percakapan sehari-hari', 'Sertifikat Bahasa Jepang Dasar', 'aktif'),
(uuid_generate_v4(), 'Bahasa Jepang Menengah', 'Program pembelajaran bahasa Jepang tingkat menengah', 6, 4500000, 'Kanji menengah, Tata bahasa kompleks, Percakapan bisnis', 'Sertifikat Bahasa Jepang Menengah', 'aktif'),
(uuid_generate_v4(), 'Pelatihan Teknik Manufaktur', 'Pelatihan keterampilan teknik untuk industri manufaktur', 2, 3000000, 'Pengoperasian mesin, Quality control, Safety procedures', 'Sertifikat Teknik Manufaktur', 'aktif'),
(uuid_generate_v4(), 'Pelatihan Budaya Kerja Jepang', 'Pengenalan budaya kerja dan etika bisnis Jepang', 1, 1500000, 'Aisatsu, Teamwork, Discipline, Work ethics', 'Sertifikat Budaya Kerja Jepang', 'aktif'),
(uuid_generate_v4(), 'Program Komprehensif', 'Program lengkap bahasa dan keterampilan teknik', 12, 8000000, 'Kombinasi semua program di atas', 'Sertifikat Program Komprehensif', 'aktif');

-- Note: The actual student data will be inserted using the generated SQL files
-- To insert the full seed data, run one of these files:
-- 1. seed-data-yutaka.sql (50 students sample)
-- 2. seed-data-full.sql (170+ students from CSV)

-- Create a view for dashboard statistics
CREATE OR REPLACE VIEW v_dashboard_statistics AS
SELECT 
    (SELECT COUNT(*) FROM siswa) as total_siswa,
    (SELECT COUNT(*) FROM siswa WHERE status_pendaftaran = 'approved') as siswa_approved,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'aktif') as siswa_aktif_jepang,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'berangkat') as siswa_berangkat,
    (SELECT COUNT(*) FROM lpk_mitra WHERE status = 'aktif') as lpk_aktif,
    (SELECT COUNT(*) FROM kumiai WHERE status = 'aktif') as kumiai_aktif,
    (SELECT COUNT(*) FROM perusahaan_penerima WHERE status = 'aktif') as perusahaan_aktif;

-- Create a view for student placement summary
CREATE OR REPLACE VIEW v_student_placement_summary AS
SELECT 
    s.id,
    s.nama_lengkap,
    s.nik,
    s.jenis_kelamin,
    s.tanggal_lahir,
    l.nama_lpk,
    p.nama_perusahaan,
    k.nama_kumiai,
    ps.tanggal_keberangkatan,
    ps.status_penempatan,
    ps.gaji_aktual,
    ps.posisi_kerja
FROM siswa s
JOIN lpk_mitra l ON s.lpk_id = l.id
LEFT JOIN penempatan_siswa ps ON s.id = ps.siswa_id
LEFT JOIN perusahaan_penerima p ON ps.perusahaan_id = p.id
LEFT JOIN kumiai k ON ps.kumiai_id = k.id;

-- Create function to get monthly statistics
CREATE OR REPLACE FUNCTION get_monthly_statistics(year_param INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE))
RETURNS TABLE (
    month_num INTEGER,
    month_name TEXT,
    new_registrations BIGINT,
    departures BIGINT,
    returns BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.month_num,
        m.month_name,
        COALESCE(reg.count, 0) as new_registrations,
        COALESCE(dep.count, 0) as departures,
        COALESCE(ret.count, 0) as returns
    FROM (
        SELECT 1 as month_num, 'Januari' as month_name
        UNION SELECT 2, 'Februari'
        UNION SELECT 3, 'Maret'
        UNION SELECT 4, 'April'
        UNION SELECT 5, 'Mei'
        UNION SELECT 6, 'Juni'
        UNION SELECT 7, 'Juli'
        UNION SELECT 8, 'Agustus'
        UNION SELECT 9, 'September'
        UNION SELECT 10, 'Oktober'
        UNION SELECT 11, 'November'
        UNION SELECT 12, 'Desember'
    ) m
    LEFT JOIN (
        SELECT 
            EXTRACT(MONTH FROM tanggal_daftar) as month_num,
            COUNT(*) as count
        FROM siswa 
        WHERE EXTRACT(YEAR FROM tanggal_daftar) = year_param
        GROUP BY EXTRACT(MONTH FROM tanggal_daftar)
    ) reg ON m.month_num = reg.month_num
    LEFT JOIN (
        SELECT 
            EXTRACT(MONTH FROM tanggal_keberangkatan) as month_num,
            COUNT(*) as count
        FROM penempatan_siswa 
        WHERE EXTRACT(YEAR FROM tanggal_keberangkatan) = year_param
        AND tanggal_keberangkatan IS NOT NULL
        GROUP BY EXTRACT(MONTH FROM tanggal_keberangkatan)
    ) dep ON m.month_num = dep.month_num
    LEFT JOIN (
        SELECT 
            EXTRACT(MONTH FROM tanggal_kepulangan) as month_num,
            COUNT(*) as count
        FROM penempatan_siswa 
        WHERE EXTRACT(YEAR FROM tanggal_kepulangan) = year_param
        AND tanggal_kepulangan IS NOT NULL
        GROUP BY EXTRACT(MONTH FROM tanggal_kepulangan)
    ) ret ON m.month_num = ret.month_num
    ORDER BY m.month_num;
END;
$$ LANGUAGE plpgsql;

-- Create function to get LPK performance
CREATE OR REPLACE FUNCTION get_lpk_performance()
RETURNS TABLE (
    lpk_id UUID,
    nama_lpk TEXT,
    total_siswa BIGINT,
    siswa_ditempatkan BIGINT,
    success_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id as lpk_id,
        l.nama_lpk,
        COUNT(s.id) as total_siswa,
        COUNT(ps.id) as siswa_ditempatkan,
        CASE 
            WHEN COUNT(s.id) > 0 THEN 
                ROUND((COUNT(ps.id)::NUMERIC / COUNT(s.id)::NUMERIC) * 100, 2)
            ELSE 0
        END as success_rate
    FROM lpk_mitra l
    LEFT JOIN siswa s ON l.id = s.lpk_id
    LEFT JOIN penempatan_siswa ps ON s.id = ps.siswa_id
    WHERE l.status = 'aktif'
    GROUP BY l.id, l.nama_lpk
    ORDER BY success_rate DESC, total_siswa DESC;
END;
$$ LANGUAGE plpgsql;

-- Insert sample user profiles (for testing)
-- Note: These will reference actual auth.users entries created through Supabase Auth
-- You'll need to create these users through the Supabase Auth interface first

-- Create RLS policies for the new views
-- Views inherit RLS from their underlying tables, so no additional policies needed

-- Grant permissions for the functions
GRANT EXECUTE ON FUNCTION get_monthly_statistics TO authenticated;
GRANT EXECUTE ON FUNCTION get_lpk_performance TO authenticated;

-- Create indexes for better performance on common queries
CREATE INDEX IF NOT EXISTS idx_siswa_tanggal_daftar ON siswa(tanggal_daftar);
CREATE INDEX IF NOT EXISTS idx_penempatan_tanggal_keberangkatan ON penempatan_siswa(tanggal_keberangkatan);
CREATE INDEX IF NOT EXISTS idx_penempatan_tanggal_kepulangan ON penempatan_siswa(tanggal_kepulangan);
CREATE INDEX IF NOT EXISTS idx_siswa_status_pendaftaran ON siswa(status_pendaftaran);
CREATE INDEX IF NOT EXISTS idx_penempatan_status ON penempatan_siswa(status_penempatan);

-- Add some sample job orders
INSERT INTO job_order (
    id, 
    perusahaan_id, 
    kumiai_id, 
    judul_pekerjaan, 
    deskripsi_pekerjaan, 
    posisi, 
    bidang_kerja,
    jenis_kelamin,
    usia_min,
    usia_max,
    pendidikan_min,
    gaji_pokok,
    tunjangan,
    jam_kerja_per_hari,
    hari_kerja_per_minggu,
    jumlah_kuota,
    kuota_terisi,
    status,
    tanggal_buka,
    tanggal_tutup
) VALUES 
(
    uuid_generate_v4(),
    (SELECT id FROM perusahaan_penerima LIMIT 1),
    (SELECT id FROM kumiai LIMIT 1),
    'Factory Worker - Manufacturing',
    'Pekerjaan di bidang manufaktur dengan sistem shift. Membutuhkan ketelitian dan kemampuan bekerja dalam tim.',
    'Production Operator',
    'Manufacturing',
    'L/P',
    20,
    35,
    'SMA',
    180000,
    20000,
    8,
    5,
    10,
    0,
    'published',
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '3 months'
);

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Seed data migration completed successfully!';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run seed-data-yutaka.sql for sample data (50 students)';
    RAISE NOTICE '2. Or run seed-data-full.sql for complete data (170+ students)';
    RAISE NOTICE '3. Create user accounts through Supabase Auth interface';
    RAISE NOTICE '4. Test the dashboard functionality';
END $$;

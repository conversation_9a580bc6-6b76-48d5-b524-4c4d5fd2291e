"use client"

import { useState } from "react"
import { Search, Plus, Edit, Trash2, Building2, MapPin, FileText } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"

interface Kumiai {
  id: number
  namaKumiai: string
  kodeKumiai: string
  alamat?: string
  jumlahPerusahaan: number
  keterangan?: string
  createdAt: string
}

// Sample data
const initialKumiaiData: Kumiai[] = [
  {
    id: 1,
    namaKumiai: "Tokyo Rodo Kumiai",
    kodeKumiai: "TRK001",
    alamat: "Shibuya, Tokyo, Japan",
    jumlahPerusahaan: 25,
    keterangan: "Kumiai utama untuk wilayah Tokyo dan sekitarnya",
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    namaKumiai: "Osaka Manufacturing Association",
    kodeKumiai: "OMA002",
    alamat: "Namba, Osaka, Japan",
    jumlahPerusahaan: 18,
    keterangan: "Spesialisasi manufaktur dan industri otomotif",
    createdAt: "2024-01-20",
  },
  {
    id: 3,
    namaKumiai: "Kyoto Agricultural Cooperative",
    kodeKumiai: "KAC003",
    alamat: "Fushimi, Kyoto, Japan",
    jumlahPerusahaan: 12,
    keterangan: "Fokus pada sektor pertanian dan perkebunan",
    createdAt: "2024-02-01",
  },
  {
    id: 4,
    namaKumiai: "Nagoya Industrial Union",
    kodeKumiai: "NIU004",
    alamat: "Sakae, Nagoya, Japan",
    jumlahPerusahaan: 22,
    keterangan: "Industri berat dan teknologi",
    createdAt: "2024-02-10",
  },
  {
    id: 5,
    namaKumiai: "Fukuoka Service Alliance",
    kodeKumiai: "FSA005",
    alamat: "Hakata, Fukuoka, Japan",
    jumlahPerusahaan: 15,
    keterangan: "Sektor jasa dan hospitality",
    createdAt: "2024-02-15",
  },
]

export default function KumiaiPage() {
  const [kumiaiData, setKumiaiData] = useState<Kumiai[]>(initialKumiaiData)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingKumiai, setEditingKumiai] = useState<Kumiai | null>(null)
  const [formData, setFormData] = useState({
    namaKumiai: "",
    kodeKumiai: "",
    alamat: "",
    keterangan: "",
  })
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({})

  // Filter data based on search term
  const filteredData = kumiaiData.filter(
    (kumiai) =>
      kumiai.namaKumiai.toLowerCase().includes(searchTerm.toLowerCase()) ||
      kumiai.kodeKumiai.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Form validation
  const validateForm = () => {
    const errors: { [key: string]: string } = {}

    if (!formData.namaKumiai.trim()) {
      errors.namaKumiai = "Nama Kumiai wajib diisi"
    }

    if (!formData.kodeKumiai.trim()) {
      errors.kodeKumiai = "Kode Kumiai wajib diisi"
    } else if (formData.kodeKumiai.length < 3) {
      errors.kodeKumiai = "Kode Kumiai minimal 3 karakter"
    }

    // Check for duplicate kode kumiai
    const isDuplicate = kumiaiData.some(
      (kumiai) =>
        kumiai.kodeKumiai.toLowerCase() === formData.kodeKumiai.toLowerCase() && kumiai.id !== editingKumiai?.id,
    )

    if (isDuplicate) {
      errors.kodeKumiai = "Kode Kumiai sudah digunakan"
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return

    if (editingKumiai) {
      // Edit existing kumiai
      setKumiaiData((prev) =>
        prev.map((kumiai) =>
          kumiai.id === editingKumiai.id
            ? {
                ...kumiai,
                namaKumiai: formData.namaKumiai,
                kodeKumiai: formData.kodeKumiai,
                alamat: formData.alamat,
                keterangan: formData.keterangan,
              }
            : kumiai,
        ),
      )
      toast({
        title: "Berhasil",
        description: "Data Kumiai berhasil diperbarui",
      })
      setIsEditModalOpen(false)
    } else {
      // Add new kumiai
      const newKumiai: Kumiai = {
        id: Math.max(...kumiaiData.map((k) => k.id)) + 1,
        namaKumiai: formData.namaKumiai,
        kodeKumiai: formData.kodeKumiai,
        alamat: formData.alamat,
        jumlahPerusahaan: 0,
        keterangan: formData.keterangan,
        createdAt: new Date().toISOString().split("T")[0],
      }
      setKumiaiData((prev) => [...prev, newKumiai])
      toast({
        title: "Berhasil",
        description: "Data Kumiai berhasil ditambahkan",
      })
      setIsAddModalOpen(false)
    }

    // Reset form
    setFormData({
      namaKumiai: "",
      kodeKumiai: "",
      alamat: "",
      keterangan: "",
    })
    setEditingKumiai(null)
    setFormErrors({})
  }

  // Handle edit
  const handleEdit = (kumiai: Kumiai) => {
    setEditingKumiai(kumiai)
    setFormData({
      namaKumiai: kumiai.namaKumiai,
      kodeKumiai: kumiai.kodeKumiai,
      alamat: kumiai.alamat || "",
      keterangan: kumiai.keterangan || "",
    })
    setFormErrors({})
    setIsEditModalOpen(true)
  }

  // Handle delete
  const handleDelete = (id: number) => {
    setKumiaiData((prev) => prev.filter((kumiai) => kumiai.id !== id))
    toast({
      title: "Berhasil",
      description: "Data Kumiai berhasil dihapus",
      variant: "destructive",
    })
  }

  // Reset form when modal closes
  const resetForm = () => {
    setFormData({
      namaKumiai: "",
      kodeKumiai: "",
      alamat: "",
      keterangan: "",
    })
    setFormErrors({})
    setEditingKumiai(null)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Master Kumiai</h1>
          <p className="text-gray-600 mt-1">Kelola data asosiasi penyalur tenaga magang dari Jepang</p>
        </div>

        <Dialog
          open={isAddModalOpen}
          onOpenChange={(open) => {
            setIsAddModalOpen(open)
            if (!open) resetForm()
          }}
        >
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Tambah Kumiai
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="text-orange-600">Tambah Kumiai Baru</DialogTitle>
              <DialogDescription>Lengkapi form di bawah untuk menambahkan data Kumiai baru</DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="namaKumiai">Nama Kumiai *</Label>
                <Input
                  id="namaKumiai"
                  value={formData.namaKumiai}
                  onChange={(e) => setFormData((prev) => ({ ...prev, namaKumiai: e.target.value }))}
                  placeholder="Masukkan nama Kumiai"
                  className={formErrors.namaKumiai ? "border-red-500" : ""}
                />
                {formErrors.namaKumiai && <p className="text-sm text-red-500">{formErrors.namaKumiai}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="kodeKumiai">Kode Kumiai *</Label>
                <Input
                  id="kodeKumiai"
                  value={formData.kodeKumiai}
                  onChange={(e) => setFormData((prev) => ({ ...prev, kodeKumiai: e.target.value.toUpperCase() }))}
                  placeholder="Contoh: TRK001"
                  className={formErrors.kodeKumiai ? "border-red-500" : ""}
                />
                {formErrors.kodeKumiai && <p className="text-sm text-red-500">{formErrors.kodeKumiai}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="alamat">Alamat</Label>
                <Input
                  id="alamat"
                  value={formData.alamat}
                  onChange={(e) => setFormData((prev) => ({ ...prev, alamat: e.target.value }))}
                  placeholder="Alamat Kumiai (opsional)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="keterangan">Keterangan</Label>
                <Textarea
                  id="keterangan"
                  value={formData.keterangan}
                  onChange={(e) => setFormData((prev) => ({ ...prev, keterangan: e.target.value }))}
                  placeholder="Keterangan tambahan (opsional)"
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                Batal
              </Button>
              <Button onClick={handleSubmit} className="bg-orange-500 hover:bg-orange-600">
                Simpan
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kumiai</CardTitle>
            <Building2 className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{kumiaiData.length}</div>
            <p className="text-xs text-muted-foreground">Asosiasi terdaftar</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Perusahaan</CardTitle>
            <FileText className="h-4 w-4 text-maroon-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-maroon-600">
              {kumiaiData.reduce((sum, kumiai) => sum + kumiai.jumlahPerusahaan, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Perusahaan mitra</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Perusahaan</CardTitle>
            <MapPin className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {kumiaiData.length > 0
                ? Math.round(kumiaiData.reduce((sum, kumiai) => sum + kumiai.jumlahPerusahaan, 0) / kumiaiData.length)
                : 0}
            </div>
            <p className="text-xs text-muted-foreground">Per Kumiai</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="text-xl text-gray-800">Daftar Kumiai</CardTitle>
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari berdasarkan nama atau kode Kumiai..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="w-16">No.</TableHead>
                  <TableHead>Nama Kumiai</TableHead>
                  <TableHead>Kode Kumiai</TableHead>
                  <TableHead>Alamat</TableHead>
                  <TableHead className="text-center">Jumlah Perusahaan</TableHead>
                  <TableHead className="text-center w-32">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      {searchTerm ? "Tidak ada data yang sesuai dengan pencarian" : "Belum ada data Kumiai"}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredData.map((kumiai, index) => (
                    <TableRow key={kumiai.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">{kumiai.namaKumiai}</div>
                          {kumiai.keterangan && <div className="text-sm text-gray-500 mt-1">{kumiai.keterangan}</div>}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-mono">
                          {kumiai.kodeKumiai}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-gray-600">
                          {kumiai.alamat ? (
                            <>
                              <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                              {kumiai.alamat}
                            </>
                          ) : (
                            <span className="text-gray-400 italic">Tidak ada alamat</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {kumiai.jumlahPerusahaan}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Dialog
                            open={isEditModalOpen && editingKumiai?.id === kumiai.id}
                            onOpenChange={(open) => {
                              if (editingKumiai?.id === kumiai.id) {
                                setIsEditModalOpen(open)
                                if (!open) resetForm()
                              }
                            }}
                          >
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(kumiai)}
                                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="sm:max-w-[500px]">
                              <DialogHeader>
                                <DialogTitle className="text-blue-600">Edit Kumiai</DialogTitle>
                                <DialogDescription>Perbarui informasi Kumiai di bawah ini</DialogDescription>
                              </DialogHeader>

                              <div className="grid gap-4 py-4">
                                <div className="space-y-2">
                                  <Label htmlFor="edit-namaKumiai">Nama Kumiai *</Label>
                                  <Input
                                    id="edit-namaKumiai"
                                    value={formData.namaKumiai}
                                    onChange={(e) => setFormData((prev) => ({ ...prev, namaKumiai: e.target.value }))}
                                    placeholder="Masukkan nama Kumiai"
                                    className={formErrors.namaKumiai ? "border-red-500" : ""}
                                  />
                                  {formErrors.namaKumiai && (
                                    <p className="text-sm text-red-500">{formErrors.namaKumiai}</p>
                                  )}
                                </div>

                                <div className="space-y-2">
                                  <Label htmlFor="edit-kodeKumiai">Kode Kumiai *</Label>
                                  <Input
                                    id="edit-kodeKumiai"
                                    value={formData.kodeKumiai}
                                    onChange={(e) =>
                                      setFormData((prev) => ({ ...prev, kodeKumiai: e.target.value.toUpperCase() }))
                                    }
                                    placeholder="Contoh: TRK001"
                                    className={formErrors.kodeKumiai ? "border-red-500" : ""}
                                  />
                                  {formErrors.kodeKumiai && (
                                    <p className="text-sm text-red-500">{formErrors.kodeKumiai}</p>
                                  )}
                                </div>

                                <div className="space-y-2">
                                  <Label htmlFor="edit-alamat">Alamat</Label>
                                  <Input
                                    id="edit-alamat"
                                    value={formData.alamat}
                                    onChange={(e) => setFormData((prev) => ({ ...prev, alamat: e.target.value }))}
                                    placeholder="Alamat Kumiai (opsional)"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label htmlFor="edit-keterangan">Keterangan</Label>
                                  <Textarea
                                    id="edit-keterangan"
                                    value={formData.keterangan}
                                    onChange={(e) => setFormData((prev) => ({ ...prev, keterangan: e.target.value }))}
                                    placeholder="Keterangan tambahan (opsional)"
                                    rows={3}
                                  />
                                </div>
                              </div>

                              <DialogFooter>
                                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                  Batal
                                </Button>
                                <Button onClick={handleSubmit} className="bg-blue-500 hover:bg-blue-600">
                                  Perbarui
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 bg-transparent"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Hapus Kumiai</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Apakah Anda yakin ingin menghapus <strong>{kumiai.namaKumiai}</strong>? Tindakan ini
                                  tidak dapat dibatalkan.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Batal</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(kumiai.id)}
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  Hapus
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {filteredData.length > 0 && (
            <div className="flex items-center justify-between px-2 py-4">
              <div className="text-sm text-muted-foreground">
                Menampilkan {filteredData.length} dari {kumiaiData.length} data
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

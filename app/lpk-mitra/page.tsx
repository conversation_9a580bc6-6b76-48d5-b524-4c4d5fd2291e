"use client"

import type React from "react"

import { useState, useMemo } from "react"
import { Search, Plus, Edit, Trash2, Building, MapPin, Phone, Mail, User, AlertTriangle } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Al<PERSON><PERSON><PERSON>og<PERSON>ooter,
  <PERSON>ert<PERSON>ialogHeader,
  <PERSON>ert<PERSON><PERSON>og<PERSON>it<PERSON>,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"

// Sample data for LPK Mitra
const initialLpkData = [
  {
    id: 1,
    namaLpk: "LPK Sukses Mandiri Jakarta",
    alamat: "Jl. Raya Kebayoran Lama No. 123",
    kota: "Jakarta Selatan",
    kontakPerson: "Budi Santoso",
    nomorTelepon: "021-7654321",
    email: "<EMAIL>",
    catatan: "LPK dengan track record terbaik untuk program magang Jepang",
    jumlahSiswa: 45,
    status: "Aktif",
  },
  {
    id: 2,
    namaLpk: "LPK Maju Bersama Bandung",
    alamat: "Jl. Dago Raya No. 456",
    kota: "Bandung",
    kontakPerson: "Siti Nurhaliza",
    nomorTelepon: "022-8765432",
    email: "<EMAIL>",
    catatan: "Spesialisasi dalam bidang manufaktur dan teknologi",
    jumlahSiswa: 32,
    status: "Aktif",
  },
  {
    id: 3,
    namaLpk: "LPK Harapan Bangsa Surabaya",
    alamat: "Jl. Pemuda No. 789",
    kota: "Surabaya",
    kontakPerson: "Ahmad Rizki",
    nomorTelepon: "031-9876543",
    email: "<EMAIL>",
    catatan: "Fokus pada pelatihan keterampilan teknis dan bahasa Jepang",
    jumlahSiswa: 28,
    status: "Aktif",
  },
  {
    id: 4,
    namaLpk: "LPK Karya Utama Medan",
    alamat: "Jl. Gatot Subroto No. 321",
    kota: "Medan",
    kontakPerson: "Dewi Sartika",
    nomorTelepon: "061-5432109",
    email: "<EMAIL>",
    catatan: "Berpengalaman dalam penempatan siswa ke perusahaan otomotif",
    jumlahSiswa: 38,
    status: "Aktif",
  },
  {
    id: 5,
    namaLpk: "LPK Nusantara Makassar",
    alamat: "Jl. AP Pettarani No. 654",
    kota: "Makassar",
    kontakPerson: "Andi Wijaya",
    nomorTelepon: "0411-876543",
    email: "<EMAIL>",
    catatan: "Kemitraan dengan berbagai perusahaan elektronik di Jepang",
    jumlahSiswa: 22,
    status: "Aktif",
  },
  {
    id: 6,
    namaLpk: "LPK Bina Prestasi Yogyakarta",
    alamat: "Jl. Malioboro No. 987",
    kota: "Yogyakarta",
    kontakPerson: "Rini Astuti",
    nomorTelepon: "0274-123456",
    email: "",
    catatan: "LPK baru dengan fasilitas pelatihan modern",
    jumlahSiswa: 15,
    status: "Aktif",
  },
]

interface LpkMitra {
  id: number
  namaLpk: string
  alamat: string
  kota: string
  kontakPerson: string
  nomorTelepon: string
  email: string
  catatan: string
  jumlahSiswa: number
  status: string
}

interface FormData {
  namaLpk: string
  alamat: string
  kota: string
  kontakPerson: string
  nomorTelepon: string
  email: string
  catatan: string
}

interface FormErrors {
  namaLpk?: string
  alamat?: string
  kota?: string
  kontakPerson?: string
  nomorTelepon?: string
  email?: string
}

export default function LpkMitraPage() {
  const [lpkData, setLpkData] = useState<LpkMitra[]>(initialLpkData)
  const [searchTerm, setSearchTerm] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingLpk, setEditingLpk] = useState<LpkMitra | null>(null)
  const [deletingLpk, setDeletingLpk] = useState<LpkMitra | null>(null)
  const [formData, setFormData] = useState<FormData>({
    namaLpk: "",
    alamat: "",
    kota: "",
    kontakPerson: "",
    nomorTelepon: "",
    email: "",
    catatan: "",
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Filter data based on search term
  const filteredData = useMemo(() => {
    return lpkData.filter(
      (lpk) =>
        lpk.namaLpk.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lpk.kota.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lpk.kontakPerson.toLowerCase().includes(searchTerm.toLowerCase()),
    )
  }, [lpkData, searchTerm])

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalLpk = lpkData.length
    const totalSiswa = lpkData.reduce((sum, lpk) => sum + lpk.jumlahSiswa, 0)
    const rataRataSiswa = totalLpk > 0 ? Math.round(totalSiswa / totalLpk) : 0
    const lpkAktif = lpkData.filter((lpk) => lpk.status === "Aktif").length

    return {
      totalLpk,
      totalSiswa,
      rataRataSiswa,
      lpkAktif,
    }
  }, [lpkData])

  // Validate form
  const validateForm = (data: FormData): FormErrors => {
    const errors: FormErrors = {}

    if (!data.namaLpk.trim()) {
      errors.namaLpk = "Nama LPK wajib diisi"
    } else if (data.namaLpk.trim().length < 3) {
      errors.namaLpk = "Nama LPK minimal 3 karakter"
    }

    if (!data.alamat.trim()) {
      errors.alamat = "Alamat wajib diisi"
    }

    if (!data.kota.trim()) {
      errors.kota = "Kota/Kabupaten wajib diisi"
    }

    if (!data.kontakPerson.trim()) {
      errors.kontakPerson = "Kontak person wajib diisi"
    }

    if (!data.nomorTelepon.trim()) {
      errors.nomorTelepon = "Nomor telepon wajib diisi"
    } else if (!/^[0-9\-+$$$$\s]+$/.test(data.nomorTelepon)) {
      errors.nomorTelepon = "Format nomor telepon tidak valid"
    }

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.email = "Format email tidak valid"
    }

    return errors
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    const errors = validateForm(formData)
    setFormErrors(errors)

    if (Object.keys(errors).length > 0) {
      setIsSubmitting(false)
      return
    }

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (editingLpk) {
        // Update existing LPK
        setLpkData((prev) =>
          prev.map((lpk) =>
            lpk.id === editingLpk.id
              ? {
                  ...lpk,
                  ...formData,
                }
              : lpk,
          ),
        )
        toast({
          title: "Berhasil",
          description: "Data LPK Mitra berhasil diperbarui",
        })
      } else {
        // Add new LPK
        const newLpk: LpkMitra = {
          id: Math.max(...lpkData.map((l) => l.id)) + 1,
          ...formData,
          jumlahSiswa: 0,
          status: "Aktif",
        }
        setLpkData((prev) => [...prev, newLpk])
        toast({
          title: "Berhasil",
          description: "LPK Mitra baru berhasil ditambahkan",
        })
      }

      handleCloseModal()
    } catch (error) {
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menyimpan data",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete
  const handleDelete = async () => {
    if (!deletingLpk) return

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))

      setLpkData((prev) => prev.filter((lpk) => lpk.id !== deletingLpk.id))
      toast({
        title: "Berhasil",
        description: `LPK ${deletingLpk.namaLpk} berhasil dihapus`,
      })
      setIsDeleteDialogOpen(false)
      setDeletingLpk(null)
    } catch (error) {
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menghapus data",
        variant: "destructive",
      })
    }
  }

  // Handle modal open/close
  const handleOpenModal = (lpk?: LpkMitra) => {
    if (lpk) {
      setEditingLpk(lpk)
      setFormData({
        namaLpk: lpk.namaLpk,
        alamat: lpk.alamat,
        kota: lpk.kota,
        kontakPerson: lpk.kontakPerson,
        nomorTelepon: lpk.nomorTelepon,
        email: lpk.email,
        catatan: lpk.catatan,
      })
    } else {
      setEditingLpk(null)
      setFormData({
        namaLpk: "",
        alamat: "",
        kota: "",
        kontakPerson: "",
        nomorTelepon: "",
        email: "",
        catatan: "",
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingLpk(null)
    setFormData({
      namaLpk: "",
      alamat: "",
      kota: "",
      kontakPerson: "",
      nomorTelepon: "",
      email: "",
      catatan: "",
    })
    setFormErrors({})
  }

  // Handle delete dialog
  const handleOpenDeleteDialog = (lpk: LpkMitra) => {
    setDeletingLpk(lpk)
    setIsDeleteDialogOpen(true)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Master LPK Mitra</h1>
          <p className="text-gray-600 mt-1">Kelola data lembaga pelatihan kerja mitra program magang Jepang</p>
        </div>
        <Button onClick={() => handleOpenModal()} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Tambah LPK Mitra
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total LPK Mitra</CardTitle>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <Building className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalLpk}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">Lembaga terdaftar</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">LPK Aktif</CardTitle>
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <Building className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.lpkAktif}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">Sedang beroperasi</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Siswa</CardTitle>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <User className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalSiswa}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">Dari semua LPK</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Rata-rata Siswa</CardTitle>
            <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
              <User className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.rataRataSiswa}</div>
            <p className="text-xs text-maroon-600 flex items-center mt-1">Per LPK</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Table */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Daftar LPK Mitra</CardTitle>
              <CardDescription>Kelola data lembaga pelatihan kerja mitra</CardDescription>
            </div>
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Cari berdasarkan nama LPK atau kota..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-center w-16">No.</TableHead>
                  <TableHead className="font-semibold">Nama LPK</TableHead>
                  <TableHead className="font-semibold">Alamat</TableHead>
                  <TableHead className="font-semibold">Kota/Kabupaten</TableHead>
                  <TableHead className="font-semibold">Kontak/No. HP</TableHead>
                  <TableHead className="font-semibold">Email</TableHead>
                  <TableHead className="font-semibold text-center">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((lpk, index) => (
                    <TableRow key={lpk.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">{lpk.namaLpk}</div>
                          {lpk.catatan && (
                            <div className="text-xs text-gray-500 mt-1 max-w-xs truncate">{lpk.catatan}</div>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {lpk.jumlahSiswa} siswa
                            </Badge>
                            <Badge
                              variant="outline"
                              className={
                                lpk.status === "Aktif"
                                  ? "bg-green-100 text-green-800 border-green-200"
                                  : "bg-gray-100 text-gray-800 border-gray-200"
                              }
                            >
                              {lpk.status}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-start gap-2">
                          <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{lpk.alamat}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium text-gray-900">{lpk.kota}</div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{lpk.kontakPerson}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{lpk.nomorTelepon}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {lpk.email ? (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{lpk.email}</span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400 italic">Tidak ada email</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenModal(lpk)}
                            className="hover:bg-blue-50 hover:border-blue-300"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenDeleteDialog(lpk)}
                            className="hover:bg-red-50 hover:border-red-300 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col items-center gap-2">
                        <Building className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">Tidak ada data LPK</h3>
                        <p className="text-gray-500">
                          {searchTerm
                            ? "Tidak ditemukan LPK yang sesuai dengan pencarian"
                            : "Belum ada LPK yang terdaftar"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer */}
          {filteredData.length > 0 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Menampilkan {filteredData.length} dari {lpkData.length} LPK Mitra
                </span>
                {searchTerm && <span className="text-blue-600">Hasil pencarian untuk "{searchTerm}"</span>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-gray-900">
              {editingLpk ? "Edit LPK Mitra" : "Tambah LPK Mitra Baru"}
            </DialogTitle>
            <DialogDescription>
              {editingLpk
                ? "Perbarui informasi LPK Mitra yang sudah ada"
                : "Masukkan informasi LPK Mitra baru untuk didaftarkan ke sistem"}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Nama LPK */}
              <div className="md:col-span-2">
                <Label htmlFor="namaLpk" className="text-sm font-medium text-gray-700">
                  Nama LPK Mitra <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="namaLpk"
                  value={formData.namaLpk}
                  onChange={(e) => setFormData({ ...formData, namaLpk: e.target.value })}
                  placeholder="Masukkan nama lengkap LPK"
                  className={formErrors.namaLpk ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.namaLpk && <p className="text-sm text-red-600 mt-1">{formErrors.namaLpk}</p>}
              </div>

              {/* Alamat */}
              <div className="md:col-span-2">
                <Label htmlFor="alamat" className="text-sm font-medium text-gray-700">
                  Alamat Lengkap <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="alamat"
                  value={formData.alamat}
                  onChange={(e) => setFormData({ ...formData, alamat: e.target.value })}
                  placeholder="Masukkan alamat lengkap LPK"
                  className={formErrors.alamat ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.alamat && <p className="text-sm text-red-600 mt-1">{formErrors.alamat}</p>}
              </div>

              {/* Kota */}
              <div>
                <Label htmlFor="kota" className="text-sm font-medium text-gray-700">
                  Kota/Kabupaten <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="kota"
                  value={formData.kota}
                  onChange={(e) => setFormData({ ...formData, kota: e.target.value })}
                  placeholder="Masukkan kota/kabupaten"
                  className={formErrors.kota ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.kota && <p className="text-sm text-red-600 mt-1">{formErrors.kota}</p>}
              </div>

              {/* Kontak Person */}
              <div>
                <Label htmlFor="kontakPerson" className="text-sm font-medium text-gray-700">
                  Kontak Person <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="kontakPerson"
                  value={formData.kontakPerson}
                  onChange={(e) => setFormData({ ...formData, kontakPerson: e.target.value })}
                  placeholder="Nama penanggung jawab"
                  className={formErrors.kontakPerson ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.kontakPerson && <p className="text-sm text-red-600 mt-1">{formErrors.kontakPerson}</p>}
              </div>

              {/* Nomor Telepon */}
              <div>
                <Label htmlFor="nomorTelepon" className="text-sm font-medium text-gray-700">
                  Nomor Telepon <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="nomorTelepon"
                  value={formData.nomorTelepon}
                  onChange={(e) => setFormData({ ...formData, nomorTelepon: e.target.value })}
                  placeholder="Contoh: 021-1234567"
                  className={formErrors.nomorTelepon ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.nomorTelepon && <p className="text-sm text-red-600 mt-1">{formErrors.nomorTelepon}</p>}
              </div>

              {/* Email */}
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email (Opsional)
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="<EMAIL>"
                  className={formErrors.email ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.email && <p className="text-sm text-red-600 mt-1">{formErrors.email}</p>}
              </div>

              {/* Catatan */}
              <div className="md:col-span-2">
                <Label htmlFor="catatan" className="text-sm font-medium text-gray-700">
                  Catatan/Keterangan (Opsional)
                </Label>
                <Textarea
                  id="catatan"
                  value={formData.catatan}
                  onChange={(e) => setFormData({ ...formData, catatan: e.target.value })}
                  placeholder="Masukkan catatan atau keterangan tambahan tentang LPK"
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={handleCloseModal} disabled={isSubmitting}>
                Batal
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-orange-500 hover:bg-orange-600 text-white">
                {isSubmitting ? "Menyimpan..." : editingLpk ? "Perbarui" : "Simpan"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Hapus
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus LPK Mitra{" "}
              <span className="font-semibold text-gray-900">"{deletingLpk?.namaLpk}"</span>?
              <br />
              <br />
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data yang terkait dengan LPK ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700 text-white">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

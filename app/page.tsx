"use client"

import { useState } from "react"
import {
  Users,
  Plane,
  Clock,
  Building2,
  TrendingUp,
  FileText,
  CheckCircle,
  AlertCircle,
  Database,
  Wifi,
  HardDrive,
  UserPlus,
  UsersIcon,
  Briefcase,
  FolderOpen,
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Bar, BarChart, ResponsiveContainer, XAxis, YAxis, Pie, PieChart, Cell } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import Link from "next/link"

// Sample data for charts
const monthlyData = [
  { month: "Jan", pendaftaran: 45, keberangkatan: 32, kepulangan: 18 },
  { month: "Feb", pendaftaran: 52, keberangkatan: 38, kepulangan: 22 },
  { month: "Mar", pendaftaran: 48, keberangkatan: 41, kepulangan: 25 },
  { month: "Apr", pendaftaran: 61, keberangkatan: 35, kepulangan: 28 },
  { month: "Mei", pendaftaran: 55, keberangkatan: 42, kepulangan: 31 },
  { month: "Jun", pendaftaran: 67, keberangkatan: 48, kepulangan: 29 },
]

const statusData = [
  { name: "Aktif di Jepang", value: 245, color: "#22c55e" },
  { name: "Menunggu Keberangkatan", value: 89, color: "#f59e0b" },
  { name: "Sudah Pulang", value: 156, color: "#6366f1" },
  { name: "Dalam Proses", value: 34, color: "#ef4444" },
]

const recentActivities = [
  {
    id: 1,
    title: "15 siswa baru mendaftar program magang",
    time: "2 jam yang lalu",
    icon: UserPlus,
    color: "text-green-600",
  },
  {
    id: 2,
    title: "Job order baru dari Toyota Motor Corp",
    time: "4 jam yang lalu",
    icon: Briefcase,
    color: "text-blue-600",
  },
  {
    id: 3,
    title: "Batch keberangkatan Maret telah dikonfirmasi",
    time: "6 jam yang lalu",
    icon: Plane,
    color: "text-orange-600",
  },
  {
    id: 4,
    title: "Dokumen 8 siswa telah diverifikasi",
    time: "1 hari yang lalu",
    icon: FileText,
    color: "text-purple-600",
  },
]

const upcomingTasks = [
  {
    id: 1,
    title: "Interview siswa batch April",
    deadline: "3 hari lagi",
    progress: 65,
    priority: "high",
  },
  {
    id: 2,
    title: "Verifikasi dokumen batch Maret",
    deadline: "5 hari lagi",
    progress: 80,
    priority: "medium",
  },
  {
    id: 3,
    title: "Koordinasi dengan Kumiai Tokyo",
    deadline: "1 minggu lagi",
    progress: 30,
    priority: "low",
  },
  {
    id: 4,
    title: "Laporan bulanan Februari",
    deadline: "2 minggu lagi",
    progress: 45,
    priority: "medium",
  },
]

export default function Dashboard() {
  const [currentTime] = useState(new Date())

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Sistem Magang Jepang</h1>
          <p className="text-gray-600 mt-1">Selamat datang di sistem manajemen penyaluran siswa magang ke Jepang</p>
          <p className="text-sm text-gray-500 mt-2">
            Terakhir diperbarui:{" "}
            {currentTime.toLocaleDateString("id-ID", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
        </div>

        <Link href="/penempatan">
          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            <TrendingUp className="w-4 h-4 mr-2" />
            Detail Penempatan
          </Button>
        </Link>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Siswa</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">524</div>
            <p className="text-xs text-muted-foreground">+12% dari bulan lalu</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif di Jepang</CardTitle>
            <Plane className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">245</div>
            <p className="text-xs text-muted-foreground">Sedang menjalani magang</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Menunggu Keberangkatan</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">89</div>
            <p className="text-xs text-muted-foreground">Dalam proses persiapan</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Perusahaan Mitra</CardTitle>
            <Building2 className="h-4 w-4 text-maroon-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-maroon-600">127</div>
            <p className="text-xs text-muted-foreground">Perusahaan aktif</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trend Chart */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Trend Bulanan</CardTitle>
            <p className="text-sm text-gray-600">Pendaftaran, keberangkatan, dan kepulangan siswa</p>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                pendaftaran: { label: "Pendaftaran", color: "#3b82f6" },
                keberangkatan: { label: "Keberangkatan", color: "#22c55e" },
                kepulangan: { label: "Kepulangan", color: "#f59e0b" },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyData}>
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="pendaftaran" fill="var(--color-pendaftaran)" radius={4} />
                  <Bar dataKey="keberangkatan" fill="var(--color-keberangkatan)" radius={4} />
                  <Bar dataKey="kepulangan" fill="var(--color-kepulangan)" radius={4} />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Status Distribution Chart */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Distribusi Status Siswa</CardTitle>
            <p className="text-sm text-gray-600">Komposisi status siswa saat ini</p>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                aktif: { label: "Aktif di Jepang", color: "#22c55e" },
                menunggu: { label: "Menunggu", color: "#f59e0b" },
                pulang: { label: "Sudah Pulang", color: "#6366f1" },
                proses: { label: "Dalam Proses", color: "#ef4444" },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <ChartTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload
                        return (
                          <div className="bg-white p-3 border rounded-lg shadow-lg">
                            <p className="font-medium">{data.name}</p>
                            <p className="text-sm text-gray-600">{data.value} siswa</p>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>

            {/* Legend */}
            <div className="grid grid-cols-2 gap-2 mt-4">
              {statusData.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }} />
                  <span className="text-xs text-gray-600">{item.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activities and Tasks Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Aktivitas Terbaru</CardTitle>
            <p className="text-sm text-gray-600">Update terkini sistem</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className={`p-2 rounded-full bg-gray-100 ${activity.color}`}>
                    <activity.icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Tasks */}
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Tugas Mendatang</CardTitle>
            <p className="text-sm text-gray-600">Jadwal dan deadline penting</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingTasks.map((task) => (
                <div key={task.id} className="p-3 rounded-lg border hover:shadow-sm transition-shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">{task.title}</h4>
                    <Badge className={getPriorityColor(task.priority)}>{task.priority}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                    <span>{task.deadline}</span>
                    <span>{task.progress}%</span>
                  </div>
                  <Progress value={task.progress} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="border-l-4 border-l-indigo-500">
        <CardHeader>
          <CardTitle className="text-lg text-gray-800">Aksi Cepat</CardTitle>
          <p className="text-sm text-gray-600">Shortcut ke fitur utama sistem</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/pendaftaran">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-blue-50 hover:border-blue-300 bg-transparent"
              >
                <UserPlus className="w-6 h-6 text-blue-600" />
                <span className="text-sm">Daftar Siswa Baru</span>
              </Button>
            </Link>

            <Link href="/siswa">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-green-50 hover:border-green-300 bg-transparent"
              >
                <UsersIcon className="w-6 h-6 text-green-600" />
                <span className="text-sm">Kelola Data Siswa</span>
              </Button>
            </Link>

            <Link href="/job-order">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-orange-50 hover:border-orange-300 bg-transparent"
              >
                <Briefcase className="w-6 h-6 text-orange-600" />
                <span className="text-sm">Job Order Baru</span>
              </Button>
            </Link>

            <Link href="/dokumen">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-purple-50 hover:border-purple-300 bg-transparent"
              >
                <FolderOpen className="w-6 h-6 text-purple-600" />
                <span className="text-sm">Cek Dokumen</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card className="border-l-4 border-l-gray-500">
        <CardHeader>
          <CardTitle className="text-lg text-gray-800">Status Sistem</CardTitle>
          <p className="text-sm text-gray-600">Monitoring kesehatan sistem</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50">
              <Database className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Database</p>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-xs text-green-600">Normal</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50">
              <Wifi className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">API Status</p>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-xs text-green-600">Online</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 rounded-lg bg-yellow-50">
              <HardDrive className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Backup</p>
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600" />
                  <span className="text-xs text-yellow-600">Scheduled</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Briefcase, Building2, Users, Calendar, Eye, Plus } from "lucide-react"

// Sample job orders data
const jobOrders = [
  {
    id: "JO-2024-001",
    namaPerusahaan: "Toyota Motor Corporation",
    posisi: "Assembly Line Operator",
    jumlahKuota: 25,
    statusJobOrder: "Terbuka",
    tanggalInput: "2024-01-10",
    siswaLolos: 25,
  },
  {
    id: "JO-2024-002",
    namaPerusahaan: "Honda Manufacturing",
    posisi: "Quality Control Inspector",
    jumlahKuota: 15,
    statusJobOrder: "Terbuka",
    tanggalInput: "2024-01-08",
    siswaLolos: 12,
  },
  {
    id: "JO-2024-003",
    namaPerusahaan: "Panasonic Electronics",
    posisi: "Electronics Assembler",
    jumlahKuota: 20,
    statusJobOrder: "Ditutup",
    tanggalInput: "2024-01-05",
    siswaLolos: 20,
  },
]

export default function JobOrderList() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                <Briefcase className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Job Order</h1>
                <p className="text-gray-600 mt-1">Kelola lowongan kerja dari perusahaan Jepang</p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg">
              <Plus className="h-4 w-4 mr-2" />
              Tambah Job Order
            </Button>
          </div>
        </div>

        {/* Job Orders Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {jobOrders.map((job) => (
            <Card key={job.id} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <Badge
                    variant="outline"
                    className={
                      job.statusJobOrder === "Terbuka"
                        ? "bg-green-100 text-green-800 border-green-200"
                        : "bg-red-100 text-red-800 border-red-200"
                    }
                  >
                    {job.statusJobOrder}
                  </Badge>
                  <span className="text-sm text-gray-500">{job.id}</span>
                </div>
                <CardTitle className="text-lg flex items-center">
                  <Building2 className="h-5 w-5 mr-2 text-orange-600" />
                  {job.namaPerusahaan}
                </CardTitle>
                <CardDescription className="text-base font-medium">{job.posisi}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Users className="h-4 w-4 text-gray-500 mr-2" />
                      <span className="text-sm text-gray-600">Kuota</span>
                    </div>
                    <span className="font-semibold">{job.jumlahKuota} orang</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                      <span className="text-sm text-gray-600">Tanggal Input</span>
                    </div>
                    <span className="text-sm">
                      {new Date(job.tanggalInput).toLocaleDateString("id-ID", {
                        day: "2-digit",
                        month: "short",
                        year: "numeric",
                      })}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Progress</span>
                    <span className="text-sm font-medium">
                      {job.siswaLolos}/{job.jumlahKuota} siswa
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-maroon-600 h-2 rounded-full"
                      style={{ width: `${(job.siswaLolos / job.jumlahKuota) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  className="w-full mt-4 border-gray-300 bg-transparent hover:bg-gray-50"
                  onClick={() => (window.location.href = `/job-order/${job.id}`)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Lihat Detail
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

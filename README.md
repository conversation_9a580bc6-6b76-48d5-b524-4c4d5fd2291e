# 🇯🇵 Dashboard Sistem Magang Jepang

<div align="center">
  <img src="public/placeholder-logo.png" alt="Logo Magang Jepang" width="200"/>
  
  **Sistem Manajemen Penyaluran Siswa Magang Indonesia ke Jepang**
  
  [![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=flat-square&logo=next.js)](https://nextjs.org/)
  [![React](https://img.shields.io/badge/React-19-blue?style=flat-square&logo=react)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4.17-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
</div>

## 📋 Daftar Isi

- [Tentang Project](#-tentang-project)
- [Fitur Utama](#-fitur-utama)
- [Teknologi](#-teknologi)
- [Instalasi](#-instalasi)
- [Penggunaan](#-penggunaan)
- [Struktur Database](#-struktur-database)
- [API Documentation](#-api-documentation)
- [Contributing](#-contributing)
- [License](#-license)

## 🎯 Tentang Project

Dashboard Sistem Magang Jepang adalah platform web komprehensif yang dirancang untuk mengelola seluruh proses penyaluran siswa magang dari Indonesia ke Jepang. Sistem ini mencakup manajemen data siswa, LPK mitra, Kumiai, job order, hingga monitoring penempatan di Jepang.

### Tujuan Utama
- 🎯 Meningkatkan efisiensi proses penyaluran siswa magang
- 📊 Menyediakan transparansi data dan reporting yang akurat
- 🔄 Mengotomatisasi workflow dari pendaftaran hingga kepulangan
- 📈 Memberikan insights bisnis melalui analytics dan dashboard

## ✨ Fitur Utama

### 📊 Dashboard & Analytics
- **Dashboard Utama**: Statistik real-time dan visualisasi data
- **Dashboard Penempatan**: Monitoring detail siswa yang sudah ditempatkan
- **Charts & Graphs**: Trend bulanan, distribusi status, dan analytics

### 👥 Manajemen Data Master
- **Data Siswa**: CRUD lengkap dengan validasi dan tracking status
- **LPK Mitra**: Manajemen Lembaga Pelatihan Kerja partner
- **Kumiai**: Data asosiasi penyalur dari Jepang
- **Job Order**: Lowongan kerja dari perusahaan Jepang
- **Dokumen**: Manajemen kelengkapan berkas siswa

### 🎓 Program Pendidikan
- **Pelatihan**: Manajemen program pelatihan dan sertifikasi
- **Jadwal**: Penjadwalan pelatihan dan monitoring progress
- **Evaluasi**: Sistem penilaian dan tracking kemajuan

### 📈 Monitoring & Reporting
- **Status Tracking**: Real-time monitoring status siswa
- **Progress Reports**: Laporan kemajuan per batch/periode
- **Analytics**: Insights dan trend analysis

## 🛠 Teknologi

### Frontend
- **Framework**: Next.js 15.2.4 (App Router)
- **UI Library**: React 19
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 3.4.17
- **Components**: Radix UI + shadcn/ui
- **Icons**: Lucide React
- **Charts**: Recharts

### Backend & Database
- **Database**: MySQL/PostgreSQL
- **ORM**: Prisma (recommended)
- **API**: Next.js API Routes
- **Authentication**: NextAuth.js (planned)

### Development Tools
- **Package Manager**: pnpm
- **Linting**: ESLint
- **Formatting**: Prettier
- **Version Control**: Git

## 🚀 Instalasi

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) atau npm/yarn
- MySQL/PostgreSQL database

### 1. Clone Repository
```bash
git clone https://github.com/your-username/dashboard-magang-jepang.git
cd dashboard-magang-jepang
```

### 2. Install Dependencies
```bash
pnpm install
# atau
npm install
```

### 3. Setup Database
```bash
# Jalankan script SQL untuk membuat schema
mysql -u username -p database_name < scripts/01-create-database-schema.sql
mysql -u username -p database_name < scripts/02-insert-initial-data.sql
mysql -u username -p database_name < scripts/03-create-views.sql
mysql -u username -p database_name < scripts/04-create-procedures.sql
```

### 4. Environment Variables
```bash
# Copy file environment
cp .env.example .env.local

# Edit file .env.local dengan konfigurasi Anda
DATABASE_URL="mysql://username:password@localhost:3306/database_name"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 5. Run Development Server
```bash
pnpm dev
# atau
npm run dev
```

Buka [http://localhost:3000](http://localhost:3000) di browser Anda.

## 📖 Penggunaan

### Login & Authentication
1. Akses halaman login di `/login`
2. Gunakan kredensial default:
   - **Admin**: `<EMAIL>`
   - **Operator**: `<EMAIL>`

### Navigasi Utama
- **Dashboard**: Overview statistik dan metrics
- **Pendaftaran**: Form pendaftaran siswa baru
- **Data Siswa**: Manajemen data siswa
- **Job Order**: Lowongan kerja dari perusahaan
- **Dokumen**: Kelengkapan berkas
- **Penempatan**: Monitoring siswa di Jepang

### Workflow Umum
1. **Pendaftaran** siswa melalui LPK
2. **Verifikasi** dokumen dan persyaratan
3. **Pelatihan** sesuai program yang dipilih
4. **Job Matching** dengan lowongan tersedia
5. **Penempatan** dan keberangkatan
6. **Monitoring** selama di Jepang
7. **Kepulangan** dan evaluasi

## 🗄 Struktur Database

### Tabel Utama
- `users` - Pengguna sistem
- `siswa` - Data siswa
- `lpk_mitra` - Lembaga Pelatihan Kerja
- `kumiai` - Asosiasi penyalur Jepang
- `job_order` - Lowongan kerja
- `penempatan_siswa` - Data penempatan
- `program_pendidikan` - Program pelatihan
- `dokumen_siswa` - Berkas siswa

### Views & Procedures
- `v_dashboard_stats` - View untuk statistik dashboard
- `GetDashboardStatistics()` - Procedure untuk metrics

Lihat file `scripts/` untuk detail lengkap schema database.

## 📚 API Documentation

### Endpoints Utama
```
GET    /api/siswa           # List siswa
POST   /api/siswa           # Tambah siswa baru
GET    /api/siswa/[id]      # Detail siswa
PUT    /api/siswa/[id]      # Update siswa
DELETE /api/siswa/[id]      # Hapus siswa

GET    /api/dashboard/stats # Statistik dashboard
GET    /api/lpk             # List LPK mitra
GET    /api/kumiai          # List Kumiai
GET    /api/job-order       # List job order
```

## 🤝 Contributing

1. Fork repository ini
2. Buat branch feature (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

### Development Guidelines
- Gunakan TypeScript untuk type safety
- Follow ESLint dan Prettier configuration
- Tulis unit tests untuk fitur baru
- Update dokumentasi jika diperlukan

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Contact

**Tim Development**
- Email: <EMAIL>
- Project Link: [https://github.com/your-username/dashboard-magang-jepang](https://github.com/your-username/dashboard-magang-jepang)

---

<div align="center">
  <p>Dibuat dengan ❤️ untuk program magang Indonesia - Jepang</p>
</div>

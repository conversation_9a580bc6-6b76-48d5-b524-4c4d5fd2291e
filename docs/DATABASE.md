# 🗄️ Database Documentation

## 📋 Daftar Isi

- [Overview](#-overview)
- [Database Schema](#-database-schema)
- [Entity Relationships](#-entity-relationships)
- [Views](#-views)
- [Stored Procedures](#-stored-procedures)
- [Indexes](#-indexes)
- [Data Migration](#-data-migration)

## 🎯 Overview

Database sistem magang Jepang menggunakan MySQL/PostgreSQL dengan struktur yang dirancang untuk mengelola seluruh lifecycle program magang dari pendaftaran hingga kepulangan.

### Key Features
- **Normalized Design**: Mengurangi redundansi data
- **Referential Integrity**: Foreign key constraints
- **Audit Trail**: Tracking perubahan data
- **Soft Delete**: Data tidak benar-benar dihapus
- **Indexing**: Optimasi query performance

## 📊 Database Schema

### Core Tables

#### 1. Users Management
```sql
-- Tabel pengguna sistem
users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'operator', 'lpk_admin', 'viewer'),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. Master Data Tables

##### LPK Mitra (Training Institutions)
```sql
lpk_mitra (
    id SERIAL PRIMARY KEY,
    nama_lpk VARCHAR(200) NOT NULL,
    alamat_lengkap TEXT NOT NULL,
    kota VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    nama_pimpinan VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100) NULL,
    website VARCHAR(200) NULL,
    status ENUM('aktif', 'nonaktif', 'suspended') DEFAULT 'aktif',
    tanggal_kerjasama DATE NULL,
    catatan TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

##### Kumiai (Japanese Cooperatives)
```sql
kumiai (
    id SERIAL PRIMARY KEY,
    nama_kumiai VARCHAR(200) NOT NULL,
    kode_kumiai VARCHAR(50) UNIQUE NOT NULL,
    alamat_jepang TEXT NOT NULL,
    kota_jepang VARCHAR(100) NOT NULL,
    prefektur VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100) NULL,
    website VARCHAR(200) NULL,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    keterangan TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. Student Data
```sql
siswa (
    id SERIAL PRIMARY KEY,
    lpk_id INT NOT NULL,
    program_pendidikan_id INT NULL,
    
    -- Personal Data
    nama_lengkap VARCHAR(200) NOT NULL,
    nik VARCHAR(20) UNIQUE NOT NULL,
    tempat_lahir VARCHAR(100) NOT NULL,
    tanggal_lahir DATE NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    agama VARCHAR(50) NOT NULL,
    status_pernikahan ENUM('belum_menikah', 'menikah', 'cerai'),
    
    -- Address
    alamat_lengkap TEXT NOT NULL,
    kelurahan VARCHAR(100) NOT NULL,
    kecamatan VARCHAR(100) NOT NULL,
    kota_kabupaten VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    kode_pos VARCHAR(10) NULL,
    
    -- Contact
    nomor_hp VARCHAR(20) NOT NULL,
    email VARCHAR(100) NULL,
    
    -- Education
    pendidikan_terakhir ENUM('SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1') NOT NULL,
    nama_sekolah VARCHAR(200) NOT NULL,
    tahun_lulus YEAR NOT NULL,
    jurusan VARCHAR(100) NULL,
    
    -- Status
    status_pendaftaran ENUM('draft', 'submitted', 'review', 'approved', 'rejected') DEFAULT 'draft',
    tanggal_daftar DATE NOT NULL,
    catatan TEXT NULL,
    
    FOREIGN KEY (lpk_id) REFERENCES lpk_mitra(id),
    FOREIGN KEY (program_pendidikan_id) REFERENCES program_pendidikan(id)
);
```

#### 4. Job Orders
```sql
job_order (
    id SERIAL PRIMARY KEY,
    perusahaan_id INT NOT NULL,
    kumiai_id INT NOT NULL,
    
    -- Job Details
    judul_pekerjaan VARCHAR(200) NOT NULL,
    deskripsi_pekerjaan TEXT NOT NULL,
    posisi VARCHAR(100) NOT NULL,
    bidang_kerja VARCHAR(100) NOT NULL,
    
    -- Requirements
    jenis_kelamin ENUM('L', 'P', 'L/P') DEFAULT 'L/P',
    usia_min INT DEFAULT 18,
    usia_max INT DEFAULT 35,
    pendidikan_min ENUM('SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1') DEFAULT 'SMA',
    
    -- Work Conditions
    gaji_pokok DECIMAL(15,2) NOT NULL,
    tunjangan DECIMAL(15,2) DEFAULT 0,
    jam_kerja_per_hari INT DEFAULT 8,
    hari_kerja_per_minggu INT DEFAULT 5,
    
    -- Quota & Status
    jumlah_kuota INT NOT NULL,
    kuota_terisi INT DEFAULT 0,
    status ENUM('draft', 'published', 'closed', 'cancelled') DEFAULT 'draft',
    tanggal_buka DATE NOT NULL,
    tanggal_tutup DATE NOT NULL,
    
    FOREIGN KEY (perusahaan_id) REFERENCES perusahaan_penerima(id),
    FOREIGN KEY (kumiai_id) REFERENCES kumiai(id)
);
```

#### 5. Placement Data
```sql
penempatan_siswa (
    id SERIAL PRIMARY KEY,
    siswa_id INT NOT NULL,
    job_order_id INT NOT NULL,
    perusahaan_id INT NOT NULL,
    kumiai_id INT NOT NULL,
    
    -- Placement Details
    tanggal_penempatan DATE NOT NULL,
    tanggal_keberangkatan DATE NULL,
    tanggal_kepulangan DATE NULL,
    status_penempatan ENUM('ditempatkan', 'berangkat', 'aktif', 'selesai', 'dibatalkan'),
    
    -- Work Details
    posisi_kerja VARCHAR(100) NOT NULL,
    gaji_aktual DECIMAL(15,2) NOT NULL,
    alamat_kerja TEXT NOT NULL,
    
    -- Monitoring
    evaluasi_bulanan TEXT NULL,
    catatan_khusus TEXT NULL,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id),
    FOREIGN KEY (job_order_id) REFERENCES job_order(id),
    FOREIGN KEY (perusahaan_id) REFERENCES perusahaan_penerima(id),
    FOREIGN KEY (kumiai_id) REFERENCES kumiai(id)
);
```

### Supporting Tables

#### Document Management
```sql
jenis_dokumen (
    id SERIAL PRIMARY KEY,
    nama_dokumen VARCHAR(100) NOT NULL,
    deskripsi TEXT NULL,
    wajib BOOLEAN DEFAULT true,
    kategori ENUM('personal', 'pendidikan', 'kesehatan', 'legal') NOT NULL,
    urutan_tampil INT DEFAULT 0
);

dokumen_siswa (
    id SERIAL PRIMARY KEY,
    siswa_id INT NOT NULL,
    jenis_dokumen_id INT NOT NULL,
    nama_file VARCHAR(255) NOT NULL,
    path_file VARCHAR(500) NOT NULL,
    ukuran_file BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    status_verifikasi ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    catatan_verifikasi TEXT NULL,
    tanggal_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id),
    FOREIGN KEY (jenis_dokumen_id) REFERENCES jenis_dokumen(id)
);
```

#### Training Programs
```sql
program_pendidikan (
    id SERIAL PRIMARY KEY,
    nama_program VARCHAR(200) NOT NULL,
    deskripsi TEXT NULL,
    durasi_bulan INT NOT NULL,
    biaya DECIMAL(15,2) NOT NULL,
    kurikulum TEXT NULL,
    sertifikat VARCHAR(200) NULL,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif'
);

jadwal_pelatihan (
    id SERIAL PRIMARY KEY,
    program_pendidikan_id INT NOT NULL,
    nama_pelatihan VARCHAR(200) NOT NULL,
    tanggal_mulai DATE NOT NULL,
    tanggal_selesai DATE NOT NULL,
    jam_mulai TIME NOT NULL,
    jam_selesai TIME NOT NULL,
    tempat VARCHAR(200) NOT NULL,
    instruktur VARCHAR(100) NULL,
    kuota_peserta INT DEFAULT 0,
    status ENUM('scheduled', 'ongoing', 'completed', 'cancelled'),
    
    FOREIGN KEY (program_pendidikan_id) REFERENCES program_pendidikan(id)
);
```

## 🔗 Entity Relationships

### Primary Relationships
1. **LPK → Siswa**: One-to-Many (Satu LPK memiliki banyak siswa)
2. **Siswa → Penempatan**: One-to-Many (Siswa bisa ditempatkan berkali-kali)
3. **Job Order → Penempatan**: One-to-Many (Satu job order untuk banyak siswa)
4. **Kumiai → Perusahaan**: One-to-Many (Satu Kumiai mengelola banyak perusahaan)
5. **Siswa → Dokumen**: One-to-Many (Siswa memiliki banyak dokumen)

### ERD Diagram
```
[LPK Mitra] ──┐
              │
              ▼
           [Siswa] ──┬── [Dokumen Siswa]
              │      │
              │      └── [Program Pendidikan]
              ▼
        [Penempatan] ──┬── [Job Order] ── [Perusahaan]
                       │                      │
                       └── [Kumiai] ──────────┘
```

## 📊 Views

### Dashboard Statistics View
```sql
CREATE VIEW v_dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM siswa) as total_siswa,
    (SELECT COUNT(*) FROM siswa WHERE status_pendaftaran = 'approved') as siswa_approved,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'aktif') as siswa_aktif_jepang,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'berangkat') as siswa_berangkat,
    (SELECT COUNT(*) FROM lpk_mitra WHERE status = 'aktif') as lpk_aktif,
    (SELECT COUNT(*) FROM kumiai WHERE status = 'aktif') as kumiai_aktif;
```

### Student Placement Summary
```sql
CREATE VIEW v_penempatan_summary AS
SELECT 
    s.id,
    s.nama_lengkap,
    l.nama_lpk,
    p.nama_perusahaan,
    k.nama_kumiai,
    ps.tanggal_keberangkatan,
    ps.status_penempatan,
    ps.gaji_aktual
FROM siswa s
JOIN lpk_mitra l ON s.lpk_id = l.id
LEFT JOIN penempatan_siswa ps ON s.id = ps.siswa_id
LEFT JOIN perusahaan_penerima p ON ps.perusahaan_id = p.id
LEFT JOIN kumiai k ON ps.kumiai_id = k.id;
```

## ⚙️ Stored Procedures

### Get Dashboard Statistics
```sql
DELIMITER //
CREATE PROCEDURE GetDashboardStatistics()
BEGIN
    SELECT 
        'Total Siswa' as metric,
        COUNT(*) as value,
        'siswa' as category
    FROM siswa
    
    UNION ALL
    
    SELECT 
        'Siswa Aktif di Jepang' as metric,
        COUNT(*) as value,
        'penempatan' as category
    FROM penempatan_siswa 
    WHERE status_penempatan = 'aktif';
END //
DELIMITER ;
```

## 📈 Indexes

### Performance Indexes
```sql
-- Siswa indexes
CREATE INDEX idx_siswa_lpk ON siswa(lpk_id);
CREATE INDEX idx_siswa_status ON siswa(status_pendaftaran);
CREATE INDEX idx_siswa_nik ON siswa(nik);

-- Penempatan indexes
CREATE INDEX idx_penempatan_siswa ON penempatan_siswa(siswa_id);
CREATE INDEX idx_penempatan_status ON penempatan_siswa(status_penempatan);
CREATE INDEX idx_penempatan_tanggal ON penempatan_siswa(tanggal_keberangkatan);

-- Job Order indexes
CREATE INDEX idx_job_order_status ON job_order(status);
CREATE INDEX idx_job_order_perusahaan ON job_order(perusahaan_id);
CREATE INDEX idx_job_order_kumiai ON job_order(kumiai_id);

-- Document indexes
CREATE INDEX idx_dokumen_siswa ON dokumen_siswa(siswa_id);
CREATE INDEX idx_dokumen_status ON dokumen_siswa(status_verifikasi);
```

## 🔄 Data Migration

### Migration Scripts Location
- `scripts/01-create-database-schema.sql` - Initial schema
- `scripts/02-insert-initial-data.sql` - Seed data
- `scripts/03-create-views.sql` - Database views
- `scripts/04-create-procedures.sql` - Stored procedures

### Migration Commands
```bash
# Create schema
mysql -u username -p database_name < scripts/01-create-database-schema.sql

# Insert initial data
mysql -u username -p database_name < scripts/02-insert-initial-data.sql

# Create views
mysql -u username -p database_name < scripts/03-create-views.sql

# Create procedures
mysql -u username -p database_name < scripts/04-create-procedures.sql
```

### Backup & Restore
```bash
# Backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# Restore
mysql -u username -p database_name < backup_20240101.sql
```

## 🔧 Maintenance

### Regular Maintenance Tasks
1. **Index Optimization**: Analyze dan optimize indexes
2. **Data Cleanup**: Remove old logs dan temporary data
3. **Statistics Update**: Update table statistics
4. **Backup Verification**: Verify backup integrity

### Performance Monitoring
- Monitor slow queries
- Check index usage
- Monitor table sizes
- Track connection usage

## 📝 Notes

### Data Types
- `SERIAL` - Auto-incrementing integer (PostgreSQL) / `AUTO_INCREMENT` (MySQL)
- `ENUM` - Enumerated values for controlled data
- `TEXT` - Variable-length text
- `DECIMAL(15,2)` - Fixed-point number for currency

### Constraints
- `NOT NULL` - Required fields
- `UNIQUE` - Unique values
- `DEFAULT` - Default values
- `FOREIGN KEY` - Referential integrity

### Best Practices
- Always use transactions for multi-table operations
- Implement soft delete for important data
- Use prepared statements to prevent SQL injection
- Regular backup and monitoring

---

**Last Updated**: 2025-01-11
**Database Version**: 1.0.0

# 🚀 Deployment Guide

## 📋 Daftar Isi

- [Overview](#-overview)
- [Prerequisites](#-prerequisites)
- [Environment Setup](#-environment-setup)
- [Database Setup](#-database-setup)
- [Vercel Deployment](#-vercel-deployment)
- [Docker Deployment](#-docker-deployment)
- [Manual Server Deployment](#-manual-server-deployment)
- [Environment Variables](#-environment-variables)
- [Post-Deployment](#-post-deployment)
- [Monitoring](#-monitoring)
- [Troubleshooting](#-troubleshooting)

## 🎯 Overview

Panduan ini menjelaskan cara deploy Dashboard Sistem Magang Jepang ke berbagai environment:
- **Development**: Local development server
- **Staging**: Testing environment
- **Production**: Live production server

## ✅ Prerequisites

### System Requirements
- Node.js 18+ 
- pnpm/npm/yarn
- MySQL 8.0+ atau PostgreSQL 13+
- Git
- Domain name (untuk production)
- SSL certificate (untuk production)

### Accounts Needed
- Vercel account (untuk Vercel deployment)
- Database hosting (PlanetScale, Supabase, atau VPS)
- Email service (SendGrid, Mailgun)
- File storage (AWS S3, Cloudinary)

## 🔧 Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/your-username/dashboard-magang-jepang.git
cd dashboard-magang-jepang
```

### 2. Install Dependencies
```bash
pnpm install
# atau
npm install
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

## 🗄️ Database Setup

### Option 1: PlanetScale (Recommended)
```bash
# Install PlanetScale CLI
curl -fsSL https://get.planetscale.com/psql | sh

# Login to PlanetScale
pscale auth login

# Create database
pscale database create magang-jepang

# Create branch
pscale branch create magang-jepang main

# Get connection string
pscale connect magang-jepang main --port 3309
```

### Option 2: Local MySQL
```bash
# Install MySQL
sudo apt install mysql-server  # Ubuntu
brew install mysql             # macOS

# Create database
mysql -u root -p
CREATE DATABASE magang_jepang;
CREATE USER 'magang_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON magang_jepang.* TO 'magang_user'@'localhost';
FLUSH PRIVILEGES;
```

### Database Migration
```bash
# Run migration scripts
mysql -u magang_user -p magang_jepang < scripts/01-create-database-schema.sql
mysql -u magang_user -p magang_jepang < scripts/02-insert-initial-data.sql
mysql -u magang_user -p magang_jepang < scripts/03-create-views.sql
mysql -u magang_user -p magang_jepang < scripts/04-create-procedures.sql
```

## ☁️ Vercel Deployment

### 1. Install Vercel CLI
```bash
npm install -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Deploy to Vercel
```bash
# First deployment
vercel

# Follow prompts:
# ? Set up and deploy "~/dashboard-magang-jepang"? [Y/n] y
# ? Which scope do you want to deploy to? [your-team]
# ? Link to existing project? [y/N] n
# ? What's your project's name? dashboard-magang-jepang
# ? In which directory is your code located? ./

# Subsequent deployments
vercel --prod
```

### 4. Configure Environment Variables
```bash
# Set environment variables
vercel env add DATABASE_URL
vercel env add NEXTAUTH_SECRET
vercel env add NEXTAUTH_URL

# Or via Vercel dashboard:
# https://vercel.com/your-team/dashboard-magang-jepang/settings/environment-variables
```

### 5. Custom Domain (Optional)
```bash
# Add custom domain
vercel domains add magangjepang.com
vercel domains add www.magangjepang.com

# Configure DNS records as instructed by Vercel
```

## 🐳 Docker Deployment

### 1. Create Dockerfile
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json pnpm-lock.yaml* ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm install -g pnpm && pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 2. Create docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: magang_jepang
      MYSQL_USER: magang_user
      MYSQL_PASSWORD: secure_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts:/docker-entrypoint-initdb.d

volumes:
  mysql_data:
```

### 3. Deploy with Docker
```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🖥️ Manual Server Deployment

### 1. Server Setup (Ubuntu 22.04)
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm
npm install -g pnpm

# Install PM2 for process management
npm install -g pm2

# Install Nginx
sudo apt install nginx

# Install MySQL
sudo apt install mysql-server
```

### 2. Application Setup
```bash
# Clone repository
git clone https://github.com/your-username/dashboard-magang-jepang.git
cd dashboard-magang-jepang

# Install dependencies
pnpm install

# Build application
pnpm build

# Setup environment
cp .env.example .env.production
# Edit .env.production with production values
```

### 3. PM2 Configuration
```bash
# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'magang-jepang',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/dashboard-magang-jepang',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 4. Nginx Configuration
```nginx
# /etc/nginx/sites-available/magangjepang.com
server {
    listen 80;
    server_name magangjepang.com www.magangjepang.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name magangjepang.com www.magangjepang.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/magangjepang.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔐 Environment Variables

### Required Variables
```bash
# Database
DATABASE_URL="mysql://user:password@host:port/database"

# Authentication
NEXTAUTH_SECRET="your-super-secret-key-here"
NEXTAUTH_URL="https://your-domain.com"

# Email (optional)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# File Upload (optional)
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES="pdf,jpg,jpeg,png,doc,docx"

# External APIs (optional)
GOOGLE_MAPS_API_KEY="your-google-maps-key"
```

### Environment-Specific Variables
```bash
# Development
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Staging
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://staging.magangjepang.com

# Production
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://magangjepang.com
```

## ✅ Post-Deployment

### 1. Health Check
```bash
# Check application status
curl -f https://your-domain.com/api/health

# Check database connection
curl -f https://your-domain.com/api/health/db
```

### 2. Initial Setup
```bash
# Create admin user (if not exists)
curl -X POST https://your-domain.com/api/setup/admin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"secure-password"}'
```

### 3. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d magangjepang.com -d www.magangjepang.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring

### 1. Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# View logs
pm2 logs magang-jepang

# Restart application
pm2 restart magang-jepang
```

### 2. Server Monitoring
```bash
# System resources
htop
df -h
free -h

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 3. Database Monitoring
```bash
# MySQL status
sudo systemctl status mysql

# Database size
mysql -u root -p -e "SELECT table_schema AS 'Database', 
ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' 
FROM information_schema.tables GROUP BY table_schema;"
```

## 🔧 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear cache
rm -rf .next
rm -rf node_modules
pnpm install
pnpm build
```

#### Database Connection Issues
```bash
# Test connection
mysql -h host -u user -p database

# Check firewall
sudo ufw status
sudo ufw allow 3306
```

#### Memory Issues
```bash
# Increase Node.js memory
export NODE_OPTIONS="--max-old-space-size=4096"

# PM2 memory restart
pm2 restart magang-jepang
```

#### SSL Issues
```bash
# Check certificate
openssl x509 -in certificate.crt -text -noout

# Renew certificate
sudo certbot renew
```

### Rollback Strategy
```bash
# Git rollback
git log --oneline
git checkout <previous-commit-hash>
pnpm build
pm2 restart magang-jepang

# Database rollback
mysql -u user -p database < backup_20240101.sql
```

---

**Last Updated**: 2025-01-11  
**Deployment Version**: 1.0.0
